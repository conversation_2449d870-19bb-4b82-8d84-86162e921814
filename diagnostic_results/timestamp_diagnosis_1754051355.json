{"timestamp": 1754051355, "diagnosis_time": "2025-08-01 14:29:15", "total_tests": 5, "passed_tests": 0, "failed_tests": 3, "error_tests": 2, "success_rate": "0.0%", "critical_issues": [{"test": "DataSnapshotValidator导入测试", "status": "FAIL", "message": "函数内部导入失败: No module named 'websocket.unified_timestamp_processor'"}, {"test": "统一时间戳处理器功能测试", "status": "ERROR", "message": "测试异常: No module named 'websocket.unified_timestamp_processor'"}, {"test": "Gate.io时间戳延迟测试", "status": "FAIL", "message": "发现极端时间戳延迟: 8900ms"}, {"test": "跨交易所时间戳同步测试", "status": "ERROR", "message": "测试异常: No module named 'websocket.unified_timestamp_processor'"}, {"test": "函数作用域问题测试", "status": "FAIL", "message": "发现3个导入作用域问题"}], "recommendations": ["将ensure_milliseconds_timestamp导入移到模块顶部", "修复Gate.io时间戳处理逻辑，减少延迟到1000ms内"], "detailed_results": [{"test_name": "DataSnapshotValidator导入测试", "status": "FAIL", "message": "函数内部导入失败: No module named 'websocket.unified_timestamp_processor'", "details": null, "error": "No module named 'websocket.unified_timestamp_processor'"}, {"test_name": "统一时间戳处理器功能测试", "status": "ERROR", "message": "测试异常: No module named 'websocket.unified_timestamp_processor'", "details": null, "error": "Traceback (most recent call last):\n  File \"/root/myproject/123/66B 修复时间戳问题，但是全是错误/diagnostic_scripts/timestamp_error_diagnosis.py\", line 108, in test_unified_timestamp_processor\n    from websocket.unified_timestamp_processor import (\nModuleNotFoundError: No module named 'websocket.unified_timestamp_processor'\n"}, {"test_name": "Gate.io时间戳延迟测试", "status": "FAIL", "message": "发现极端时间戳延迟: 8900ms", "details": {"正常延迟": {"gate_timestamp": 1754051355665, "okx_timestamp": 1754051355665, "time_diff_ms": 0, "is_synced": true, "threshold_ms": 800}, "轻微延迟": {"gate_timestamp": 1754051354765, "okx_timestamp": 1754051355665, "time_diff_ms": 900, "is_synced": false, "threshold_ms": 800}, "严重延迟": {"gate_timestamp": 1754051350765, "okx_timestamp": 1754051355665, "time_diff_ms": 4900, "is_synced": false, "threshold_ms": 800}, "极端延迟": {"gate_timestamp": 1754051346765, "okx_timestamp": 1754051355665, "time_diff_ms": 8900, "is_synced": false, "threshold_ms": 800}}, "error": null}, {"test_name": "跨交易所时间戳同步测试", "status": "ERROR", "message": "测试异常: No module named 'websocket.unified_timestamp_processor'", "details": null, "error": "Traceback (most recent call last):\n  File \"/root/myproject/123/66B 修复时间戳问题，但是全是错误/diagnostic_scripts/timestamp_error_diagnosis.py\", line 220, in test_cross_exchange_sync\n    from websocket.unified_timestamp_processor import get_timestamp_processor\nModuleNotFoundError: No module named 'websocket.unified_timestamp_processor'\n"}, {"test_name": "函数作用域问题测试", "status": "FAIL", "message": "发现3个导入作用域问题", "details": {"top_level_import": {"status": "error", "error": "No module named 'websocket.unified_timestamp_processor'"}, "internal_import": {"status": "error", "error": "No module named 'websocket.unified_timestamp_processor'"}, "conditional_import": {"status": "error", "error": "No module named 'websocket.unified_timestamp_processor'"}}, "error": null}]}