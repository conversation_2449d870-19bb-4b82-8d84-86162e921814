{"test_timestamp": 1754052460, "test_time": "2025-08-01 14:47:40", "total_execution_time": 0.03861427307128906, "quality_grade": "⚠️ 需要改进", "quality_level": "NEEDS_IMPROVEMENT", "overall_statistics": {"total_tests": 15, "passed_tests": 0, "failed_tests": 0, "error_tests": 15, "success_rate": 0.0}, "stage_statistics": {"stage1_basic_core": {"total": 5, "passed": 0, "success_rate": 0.0}, "stage2_system_integration": {"total": 5, "passed": 0, "success_rate": 0.0}, "stage3_production_simulation": {"total": 5, "passed": 0, "success_rate": 0.0}}, "coverage_statistics": {"unique_coverage_points": 1, "total_coverage_points": 1, "coverage_details": ["初始内存测量"]}, "detailed_results": [{"test_name": "DataSnapshotValidator核心功能", "status": "ERROR", "execution_time": 0.0002982616424560547, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "时间戳函数边界测试", "status": "ERROR", "execution_time": 0.00014495849609375, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "统一模块完整性测试", "status": "ERROR", "execution_time": 0.015793800354003906, "details": null, "error": "No module named 'websocket.unified_timestamp_processor'", "coverage_points": []}, {"test_name": "错误处理机制测试", "status": "ERROR", "execution_time": 0.0001747608184814453, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "内存性能基准测试", "status": "ERROR", "execution_time": 0.021255970001220703, "details": null, "error": "No module named 'core'", "coverage_points": ["初始内存测量"]}, {"test_name": "跨模块数据流测试", "status": "ERROR", "execution_time": 0.0001316070556640625, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "多交易所时间戳同步测试", "status": "ERROR", "execution_time": 6.222724914550781e-05, "details": null, "error": "No module named 'websocket.unified_timestamp_processor'", "coverage_points": []}, {"test_name": "并发操作安全性测试", "status": "ERROR", "execution_time": 9.036064147949219e-05, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "状态一致性测试", "status": "ERROR", "execution_time": 8.845329284667969e-05, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "极端条件协调测试", "status": "ERROR", "execution_time": 0.000102996826171875, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "真实世界时间戳场景测试", "status": "ERROR", "execution_time": 9.107589721679688e-05, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "高频并发压力测试", "status": "ERROR", "execution_time": 8.511543273925781e-05, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "网络延迟波动模拟测试", "status": "ERROR", "execution_time": 3.0040740966796875e-05, "details": null, "error": "No module named 'websocket.unified_timestamp_processor'", "coverage_points": []}, {"test_name": "内存泄漏长期运行测试", "status": "ERROR", "execution_time": 8.58306884765625e-05, "details": null, "error": "No module named 'core'", "coverage_points": []}, {"test_name": "故障恢复能力测试", "status": "ERROR", "execution_time": 8.416175842285156e-05, "details": null, "error": "No module named 'core'", "coverage_points": []}], "critical_failures": [{"test_name": "DataSnapshotValidator核心功能", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "时间戳函数边界测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "统一模块完整性测试", "status": "ERROR", "error": "No module named 'websocket.unified_timestamp_processor'"}, {"test_name": "错误处理机制测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "内存性能基准测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "跨模块数据流测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "多交易所时间戳同步测试", "status": "ERROR", "error": "No module named 'websocket.unified_timestamp_processor'"}, {"test_name": "并发操作安全性测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "状态一致性测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "极端条件协调测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "真实世界时间戳场景测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "高频并发压力测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "网络延迟波动模拟测试", "status": "ERROR", "error": "No module named 'websocket.unified_timestamp_processor'"}, {"test_name": "内存泄漏长期运行测试", "status": "ERROR", "error": "No module named 'core'"}, {"test_name": "故障恢复能力测试", "status": "ERROR", "error": "No module named 'core'"}]}