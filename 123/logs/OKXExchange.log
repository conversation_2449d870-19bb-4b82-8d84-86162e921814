2025-08-01 15:35:05 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-01 15:35:05 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-01 15:35:05 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-01 15:35:05 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-01 15:35:05.120 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-01 15:35:05.121 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-01 15:35:05 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-01 15:35:05 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-01 15:35:05 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-01 15:35:05 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-01 15:35:05.573 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:35:05.903 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:05.904 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:06.243 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:35:06.243 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:35:06 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-01 15:35:06.243 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:35:06.559 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:06.559 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:06.898 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:35:06.899 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:35:06 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-01 15:35:06.899 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:35:07.228 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:07.228 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:07.557 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:35:07.557 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:35:07 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-01 15:35:07.558 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:35:07.902 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:07.902 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:08.221 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:35:08.223 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:35:08 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-01 15:35:08 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-01 15:35:08.543 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 15:35:09.906 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 15:35:11.417 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:35:12.079 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:35:12.650 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:35:12.986 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:35:13.554 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:35:14.125 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:35:33.990 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:33.990 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:34.644 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:34.644 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:35.319 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:35.319 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:35.963 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:35.963 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:36.628 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:36.628 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:35:37.284 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:35:37.285 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:19.622 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 15:36:19.956 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 15:36:19.983 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:19.983 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-01 15:36:19.989 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:36:22.391 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-01 15:36:22.722 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-01 15:36:22.723 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-01 15:36:22.732 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-01 15:36:22.745 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-01 15:36:23.747 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.081 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.081 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.082 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.082 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.082 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.082 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.083 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.083 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.083 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 15:36:24.155 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:24.156 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:24.489 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:24.490 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:24.494 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:24.494 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:24.495 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:24.495 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:24.500 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 15:36:24.500 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 15:36:24.500 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 15:36:24.500 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 15:36:24.500 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 15:36:24.501 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 15:36:24.501 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 15:36:24.501 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 15:36:24 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 15:36:24.504 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:24.504 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:24.516 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:24.517 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:24.517 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 15:36:24.517 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 15:36:24.517 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 15:36:24.517 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 15:36:24.517 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 15:36:24.517 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 15:36:24.518 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 15:36:24.518 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 15:36:24 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 15:36:24.527 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 15:36:24.527 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 15:36:24.528 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 15:36:24.528 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 15:36:24.528 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 15:36:24.528 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 15:36:24.528 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 15:36:24.529 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 15:36:24 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 15:36:24.530 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 15:36:24.530 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 15:36:24.531 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 15:36:24.531 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 15:36:24.531 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 15:36:24.531 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 15:36:24.531 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 15:36:24.531 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 15:36:24 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 15:36:24.546 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 15:36:24.546 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 15:36:24.547 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 15:36:24.547 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 15:36:24.547 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 15:36:24.547 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 15:36:24.548 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 15:36:24.548 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 15:36:24 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 15:36:24.823 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:24.824 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:24.828 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:24.829 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:24.835 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:24.835 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:24.955 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:24.955 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:26.577 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:26.577 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:26.900 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:26.900 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:26.904 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:26.904 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:26.905 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:26.905 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:26.906 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:26.907 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:26.913 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 15:36:26.914 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 15:36:27.236 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:27.236 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:27.248 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:27.248 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:27.249 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:27.249 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:27.252 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 15:36:27.252 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 15:36:36.953 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 15:36:38.457 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 15:36:39.953 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:40.614 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:41.208 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:41.514 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:36:42.087 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:36:42.670 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:36:47.460 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 15:36:48.976 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 15:36:50.456 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:51.103 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:51.690 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 15:36:52.064 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 15:36:52.639 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
