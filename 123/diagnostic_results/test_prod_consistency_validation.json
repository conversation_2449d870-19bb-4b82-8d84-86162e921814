{"timestamp": 1754043486200, "total_stages": 6, "passed_stages": 6, "success_rate": 100.0, "overall_status": "CONSISTENT", "test_results": [{"test": "network_environment_simulation", "passed": true, "delay_results": [{"target_delay_ms": 50, "actual_delay_ms": 53.52187156677246, "max_timestamp_age_ms": 12.5, "timestamps": {"gate": 1754043435970, "bybit": 1754043435980, "okx": 1754043435980}, "acceptable": true}, {"target_delay_ms": 100, "actual_delay_ms": 101.06277465820312, "max_timestamp_age_ms": 3.6796875, "timestamps": {"gate": 1754043436080, "bybit": 1754043436080, "okx": 1754043436080}, "acceptable": true}, {"target_delay_ms": 200, "actual_delay_ms": 201.1580467224121, "max_timestamp_age_ms": 4.966796875, "timestamps": {"gate": 1754043436280, "bybit": 1754043436280, "okx": 1754043436280}, "acceptable": true}, {"target_delay_ms": 500, "actual_delay_ms": 501.6613006591797, "max_timestamp_age_ms": 6.69921875, "timestamps": {"gate": 1754043436780, "bybit": 1754043436780, "okx": 1754043436780}, "acceptable": true}, {"target_delay_ms": 1000, "actual_delay_ms": 1002.0921230316162, "max_timestamp_age_ms": 8.873779296875, "timestamps": {"gate": **********780, "bybit": **********780, "okx": **********780}, "acceptable": true}], "interrupt_results": [{"duration_seconds": 1, "before_timestamps": {"gate": 1754043440110, "bybit": 1754043440090, "okx": 1754043440090}, "after_timestamps": {"gate": 1754043441110, "bybit": 1754043441090, "okx": 1754043441090}, "time_progression_normal": true}, {"duration_seconds": 5, "before_timestamps": {"gate": 1754043441110, "bybit": 1754043441090, "okx": 1754043441090}, "after_timestamps": {"gate": 1754043446110, "bybit": 1754043446090, "okx": 1754043446090}, "time_progression_normal": true}, {"duration_seconds": 10, "before_timestamps": {"gate": 1754043446110, "bybit": 1754043446090, "okx": 1754043446090}, "after_timestamps": {"gate": 1754043456130, "bybit": 1754043456110, "okx": 1754043456110}, "time_progression_normal": true}], "details": "网络环境模拟测试"}, {"test": "timestamp_sync_health", "passed": true, "health_status": {"gate": {"exchange": "gate", "time_synced": true, "time_offset_ms": -16, "last_sync_time": **********.7891095, "sync_age_seconds": 18.361375331878662, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": true, "health_level": "GOOD"}, "bybit": {"exchange": "bybit", "time_synced": true, "time_offset_ms": -37, "last_sync_time": **********.8790188, "sync_age_seconds": 17.271475315093994, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": true, "health_level": "GOOD"}, "okx": {"exchange": "okx", "time_synced": true, "time_offset_ms": -32, "last_sync_time": **********.9974685, "sync_age_seconds": 16.153029918670654, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": true, "health_level": "GOOD"}}, "critical_count": 0, "error_count": 0, "details": "时间戳同步健康检查"}, {"test": "data_flow_processing", "passed": true, "success_rate": 1.0, "total_processed": 30, "successful_processed": 30, "processing_results": [{"batch": 0, "results": {"gate": {"timestamp": 1754043456150, "expected": 1754043456150.8445, "diff_ms": 0.844482421875, "reasonable": true}, "bybit": {"timestamp": 1754043456150, "expected": 1754043456150.8445, "diff_ms": 0.844482421875, "reasonable": true}, "okx": {"timestamp": 1754043456110, "expected": 1754043456150.8445, "diff_ms": 40.844482421875, "reasonable": true}}}, {"batch": 1, "results": {"gate": {"timestamp": 1754043456050, "expected": 1754043456050.8447, "diff_ms": 0.**********, "reasonable": true}, "bybit": {"timestamp": 1754043456050, "expected": 1754043456050.8447, "diff_ms": 0.**********, "reasonable": true}, "okx": {"timestamp": 1754043456110, "expected": 1754043456050.8447, "diff_ms": 59.**********, "reasonable": true}}}, {"batch": 2, "results": {"gate": {"timestamp": 1754043455950, "expected": 1754043455950.8445, "diff_ms": 0.844482421875, "reasonable": true}, "bybit": {"timestamp": 1754043455950, "expected": 1754043455950.8445, "diff_ms": 0.844482421875, "reasonable": true}, "okx": {"timestamp": 1754043456110, "expected": 1754043455950.8445, "diff_ms": 159.155517578125, "reasonable": true}}}, {"batch": 3, "results": {"gate": {"timestamp": 1754043455850, "expected": 1754043455850.8447, "diff_ms": 0.**********, "reasonable": true}, "bybit": {"timestamp": 1754043455850, "expected": 1754043455850.8447, "diff_ms": 0.**********, "reasonable": true}, "okx": {"timestamp": 1754043456110, "expected": 1754043455850.8447, "diff_ms": 259.**********, "reasonable": true}}}, {"batch": 4, "results": {"gate": {"timestamp": 1754043455750, "expected": 1754043455750.8445, "diff_ms": 0.844482421875, "reasonable": true}, "bybit": {"timestamp": 1754043455750, "expected": 1754043455750.8445, "diff_ms": 0.844482421875, "reasonable": true}, "okx": {"timestamp": 1754043456110, "expected": 1754043455750.8445, "diff_ms": 359.155517578125, "reasonable": true}}}], "details": "实际数据流处理验证"}, {"test": "error_handling_recovery", "passed": true, "avg_recovery_rate": 1.0, "recovery_results": [{"scenario": "无效时间戳数据", "recovery_rate": 1.0, "results": {"gate": {"recovered": true, "timestamp": 1754043456130, "age_ms": 23.515380859375}, "bybit": {"recovered": true, "timestamp": 1754043456110, "age_ms": 43.58984375}, "okx": {"recovered": true, "timestamp": 1754043456110, "age_ms": 43.6416015625}}}, {"scenario": "空数据", "recovery_rate": 1.0, "results": {"gate": {"recovered": true, "timestamp": 1754043456130, "age_ms": 23.728515625}, "bybit": {"recovered": true, "timestamp": 1754043456110, "age_ms": 43.75244140625}, "okx": {"recovered": true, "timestamp": 1754043456110, "age_ms": 43.7744140625}}}, {"scenario": "None数据", "recovery_rate": 1.0, "results": {"gate": {"recovered": true, "timestamp": 1754043456130, "age_ms": 23.90576171875}, "bybit": {"recovered": true, "timestamp": 1754043456110, "age_ms": 43.929443359375}, "okx": {"recovered": true, "timestamp": 1754043456110, "age_ms": 43.951904296875}}}, {"scenario": "超大时间戳", "recovery_rate": 1.0, "results": {"gate": {"recovered": true, "timestamp": 1754043456130, "age_ms": 24.018310546875}, "bybit": {"recovered": true, "timestamp": 1754043456110, "age_ms": 44.354248046875}, "okx": {"recovered": true, "timestamp": 1754043456110, "age_ms": 44.6572265625}}}, {"scenario": "负数时间戳", "recovery_rate": 1.0, "results": {"gate": {"recovered": true, "timestamp": 1754043456130, "age_ms": 25.2822265625}, "bybit": {"recovered": true, "timestamp": 1754043456110, "age_ms": 45.817626953125}, "okx": {"recovered": true, "timestamp": 1754043456110, "age_ms": 46.008056640625}}}, {"scenario": "字符串时间戳", "recovery_rate": 1.0, "results": {"gate": {"recovered": true, "timestamp": 1754043456130, "age_ms": 26.09521484375}, "bybit": {"recovered": true, "timestamp": 1754043456110, "age_ms": 46.26318359375}, "okx": {"recovered": true, "timestamp": 1754043456110, "age_ms": 46.4287109375}}}], "details": "错误处理和恢复验证"}, {"test": "long_term_stability", "passed": true, "duration_seconds": 30, "total_samples": 30, "valid_samples": 30, "availability_rate": 1.0, "avg_max_diff_ms": 19.666666666666668, "max_max_diff_ms": 20, "details": "长期稳定性验证"}, {"test": "complete_arbitrage_flow", "passed": true, "success_rate": 1.0, "successful_flows": 3, "total_flows": 3, "flow_results": [{"scenario": "标准套利机会", "success": true, "timestamp_results": {"gate_spot": {"timestamp": 1754043486100, "age_ms": 100.2001953125, "fresh": true}, "bybit_futures": {"timestamp": 1754043486050, "age_ms": 150.298828125, "fresh": true}}, "max_time_diff_ms": 50, "all_fresh": true, "time_sync_ok": true}, {"scenario": "时间戳不同步场景", "success": true, "timestamp_results": {"gate_spot": {"timestamp": 1754043486180, "age_ms": 20.465576171875, "fresh": true}, "okx_futures": {"timestamp": 1754043486100, "age_ms": 100.529296875, "fresh": true}}, "max_time_diff_ms": 80, "all_fresh": true, "time_sync_ok": true}, {"scenario": "网络延迟场景", "success": true, "timestamp_results": {"bybit_spot": {"timestamp": 1754043485200, "age_ms": 1000.69970703125, "fresh": true}, "gate_futures": {"timestamp": 1754043485400, "age_ms": 800.760986328125, "fresh": true}}, "max_time_diff_ms": 200, "all_fresh": true, "time_sync_ok": true}], "details": "完整套利流程验证"}]}