#!/usr/bin/env python3
"""
WebSocket时间戳同步问题精确诊断脚本
分析日志中的时间戳不同步问题，精确定位根本原因
"""

import json
import re
import sys
import os
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_websocket_performance_log(log_file: str) -> Dict[str, Any]:
    """分析WebSocket性能日志，提取时间戳同步问题"""
    
    print(f"🔍 分析日志文件: {log_file}")
    
    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        return {}
    
    # 统计数据
    stats = {
        "total_lines": 0,
        "timestamp_sync_issues": 0,
        "cross_exchange_sync_issues": 0,
        "arbitrage_opportunities_discarded": 0,
        "exchange_sync_status": defaultdict(int),
        "time_diff_distribution": defaultdict(int),
        "exchange_pairs": defaultdict(int),
        "max_time_diff": 0,
        "min_time_diff": float('inf'),
        "avg_time_diff": 0,
        "sync_failures_by_exchange": defaultdict(int),
        "detailed_issues": []
    }
    
    time_diffs = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                stats["total_lines"] += 1
                line = line.strip()
                
                if not line:
                    continue
                
                # 解析日志行
                try:
                    # 提取时间戳
                    timestamp_match = re.match(r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})', line)
                    if not timestamp_match:
                        continue
                    
                    log_timestamp = timestamp_match.group(1)
                    
                    # 分析不同类型的时间戳同步问题
                    if "交易所时间戳未同步" in line:
                        stats["timestamp_sync_issues"] += 1
                        
                        # 提取交易所信息
                        if "exchange" in line:
                            exchange_match = re.search(r"'exchange': '([^']+)'", line)
                            if exchange_match:
                                exchange = exchange_match.group(1)
                                stats["sync_failures_by_exchange"][exchange] += 1
                        
                        # 提取同步状态
                        if "sync_status" in line:
                            status_match = re.search(r"'sync_status': '([^']+)'", line)
                            if status_match:
                                status = status_match.group(1)
                                stats["exchange_sync_status"][status] += 1
                    
                    elif "跨交易所时间戳不同步" in line:
                        stats["cross_exchange_sync_issues"] += 1
                        
                        # 提取时间差信息
                        time_diff_match = re.search(r"'time_diff_ms': ([0-9.]+)", line)
                        if time_diff_match:
                            time_diff = float(time_diff_match.group(1))
                            time_diffs.append(time_diff)
                            
                            stats["max_time_diff"] = max(stats["max_time_diff"], time_diff)
                            stats["min_time_diff"] = min(stats["min_time_diff"], time_diff)
                            
                            # 时间差分布统计
                            if time_diff < 1000:
                                stats["time_diff_distribution"]["<1s"] += 1
                            elif time_diff < 2000:
                                stats["time_diff_distribution"]["1-2s"] += 1
                            elif time_diff < 5000:
                                stats["time_diff_distribution"]["2-5s"] += 1
                            elif time_diff < 10000:
                                stats["time_diff_distribution"]["5-10s"] += 1
                            else:
                                stats["time_diff_distribution"][">10s"] += 1
                        
                        # 提取交易所对信息
                        exchange1_match = re.search(r"'exchange1': '([^']+)'", line)
                        exchange2_match = re.search(r"'exchange2': '([^']+)'", line)
                        if exchange1_match and exchange2_match:
                            exchange1 = exchange1_match.group(1)
                            exchange2 = exchange2_match.group(1)
                            pair_key = f"{exchange1}-{exchange2}"
                            stats["exchange_pairs"][pair_key] += 1
                    
                    elif "价格数据时间戳不同步，丢弃套利机会" in line:
                        stats["arbitrage_opportunities_discarded"] += 1
                        
                        # 提取详细信息用于分析
                        combo_match = re.search(r"'combo_name': '([^']+)'", line)
                        spot_exchange_match = re.search(r"'spot_exchange': '([^']+)'", line)
                        futures_exchange_match = re.search(r"'futures_exchange': '([^']+)'", line)
                        time_diff_match = re.search(r"'time_diff_ms': ([0-9.]+)", line)
                        
                        if all([combo_match, spot_exchange_match, futures_exchange_match, time_diff_match]):
                            issue_detail = {
                                "log_timestamp": log_timestamp,
                                "combo_name": combo_match.group(1),
                                "spot_exchange": spot_exchange_match.group(1),
                                "futures_exchange": futures_exchange_match.group(1),
                                "time_diff_ms": float(time_diff_match.group(1)),
                                "line_number": line_num
                            }
                            stats["detailed_issues"].append(issue_detail)
                
                except Exception as e:
                    print(f"⚠️ 解析第{line_num}行失败: {e}")
                    continue
    
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")
        return {}
    
    # 计算平均时间差
    if time_diffs:
        stats["avg_time_diff"] = sum(time_diffs) / len(time_diffs)
        stats["min_time_diff"] = min(time_diffs) if stats["min_time_diff"] == float('inf') else stats["min_time_diff"]
    else:
        stats["min_time_diff"] = 0
    
    return stats

def generate_diagnosis_report(stats: Dict[str, Any]) -> str:
    """生成诊断报告"""
    
    report = []
    report.append("=" * 80)
    report.append("🔍 WebSocket时间戳同步问题诊断报告")
    report.append("=" * 80)
    report.append("")
    
    # 基本统计
    report.append("📊 基本统计:")
    report.append(f"  • 总日志行数: {stats['total_lines']:,}")
    report.append(f"  • 时间戳同步问题: {stats['timestamp_sync_issues']:,}")
    report.append(f"  • 跨交易所同步问题: {stats['cross_exchange_sync_issues']:,}")
    report.append(f"  • 丢弃的套利机会: {stats['arbitrage_opportunities_discarded']:,}")
    report.append("")
    
    # 严重程度评估
    total_issues = stats['timestamp_sync_issues'] + stats['cross_exchange_sync_issues']
    if total_issues > 1000:
        severity = "🚨 CRITICAL"
        color = "红色"
    elif total_issues > 100:
        severity = "⚠️ HIGH"
        color = "橙色"
    elif total_issues > 10:
        severity = "⚠️ MEDIUM"
        color = "黄色"
    else:
        severity = "✅ LOW"
        color = "绿色"
    
    report.append(f"🎯 问题严重程度: {severity} ({color})")
    report.append(f"  • 总问题数: {total_issues:,}")
    report.append(f"  • 问题密度: {total_issues/max(stats['total_lines'], 1)*100:.2f}%")
    report.append("")
    
    # 交易所同步状态分析
    if stats['exchange_sync_status']:
        report.append("🏢 交易所同步状态分析:")
        for status, count in stats['exchange_sync_status'].items():
            percentage = count / stats['timestamp_sync_issues'] * 100 if stats['timestamp_sync_issues'] > 0 else 0
            report.append(f"  • {status}: {count:,} ({percentage:.1f}%)")
        report.append("")
    
    # 同步失败按交易所统计
    if stats['sync_failures_by_exchange']:
        report.append("📈 同步失败按交易所统计:")
        sorted_exchanges = sorted(stats['sync_failures_by_exchange'].items(), key=lambda x: x[1], reverse=True)
        for exchange, count in sorted_exchanges:
            percentage = count / stats['timestamp_sync_issues'] * 100 if stats['timestamp_sync_issues'] > 0 else 0
            report.append(f"  • {exchange}: {count:,} ({percentage:.1f}%)")
        report.append("")
    
    # 时间差分析
    if stats['max_time_diff'] > 0:
        report.append("⏱️ 时间差分析:")
        report.append(f"  • 最大时间差: {stats['max_time_diff']:.1f}ms")
        report.append(f"  • 最小时间差: {stats['min_time_diff']:.1f}ms")
        report.append(f"  • 平均时间差: {stats['avg_time_diff']:.1f}ms")
        report.append("")
        
        if stats['time_diff_distribution']:
            report.append("📊 时间差分布:")
            for range_key, count in stats['time_diff_distribution'].items():
                percentage = count / stats['cross_exchange_sync_issues'] * 100 if stats['cross_exchange_sync_issues'] > 0 else 0
                report.append(f"  • {range_key}: {count:,} ({percentage:.1f}%)")
            report.append("")
    
    # 交易所对分析
    if stats['exchange_pairs']:
        report.append("🔗 问题交易所对分析:")
        sorted_pairs = sorted(stats['exchange_pairs'].items(), key=lambda x: x[1], reverse=True)
        for pair, count in sorted_pairs[:10]:  # 显示前10个
            percentage = count / stats['cross_exchange_sync_issues'] * 100 if stats['cross_exchange_sync_issues'] > 0 else 0
            report.append(f"  • {pair}: {count:,} ({percentage:.1f}%)")
        report.append("")
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🚀 启动WebSocket时间戳同步问题诊断...")
    
    # 日志文件路径
    log_file = "logs/websocket_performance_20250801.log"
    
    # 分析日志
    stats = analyze_websocket_performance_log(log_file)
    
    if not stats:
        print("❌ 无法分析日志文件")
        return
    
    # 生成报告
    report = generate_diagnosis_report(stats)
    print(report)
    
    # 保存诊断结果
    output_file = "diagnostic_scripts/websocket_timestamp_sync_diagnosis_result.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False, default=str)
        print(f"✅ 诊断结果已保存到: {output_file}")
    except Exception as e:
        print(f"⚠️ 保存诊断结果失败: {e}")

    # 关键问题总结
    print("\n" + "=" * 80)
    print("🎯 关键问题总结:")
    print("=" * 80)

    if stats['timestamp_sync_issues'] > 0:
        print(f"1. 🚨 交易所时间戳未同步: {stats['timestamp_sync_issues']:,} 次")
        print("   - 根本原因: 时间戳同步机制失效")
        print("   - 影响: 所有交易所使用统一时间基准，失去精确时间戳")

    if stats['cross_exchange_sync_issues'] > 0:
        print(f"2. ⚠️ 跨交易所时间戳不同步: {stats['cross_exchange_sync_issues']:,} 次")
        print(f"   - 平均时间差: {stats['avg_time_diff']:.1f}ms (阈值: 800ms)")
        print("   - 影响: 大量套利机会被错误丢弃")

    if stats['arbitrage_opportunities_discarded'] > 0:
        print(f"3. 💰 丢弃的套利机会: {stats['arbitrage_opportunities_discarded']:,} 次")
        print("   - 直接影响: 系统无法执行套利交易")
        print("   - 经济损失: 错失大量盈利机会")

    print("\n🔧 建议修复方向:")
    print("1. 检查时间戳同步机制的初始化和维护")
    print("2. 优化跨交易所时间戳对齐算法")
    print("3. 调整时间戳同步阈值和重试机制")
    print("4. 强化时间戳处理的错误恢复能力")

    # 详细问题分析
    if stats['detailed_issues']:
        print("\n📋 详细问题分析 (前10个):")
        print("-" * 80)
        for i, issue in enumerate(stats['detailed_issues'][:10], 1):
            print(f"{i}. {issue['log_timestamp']} - {issue['combo_name']}")
            print(f"   现货: {issue['spot_exchange']} | 期货: {issue['futures_exchange']}")
            print(f"   时间差: {issue['time_diff_ms']:.1f}ms | 行号: {issue['line_number']}")
            print()

    # 修复优先级建议
    print("🚀 修复优先级建议:")
    print("=" * 80)

    # 根据问题严重程度给出优先级
    if stats['timestamp_sync_issues'] > stats['cross_exchange_sync_issues']:
        print("🔥 优先级1: 修复时间戳同步机制")
        print("   - 问题: 交易所时间戳同步失效")
        print("   - 修复: 检查TimestampProcessor初始化和同步逻辑")
        print("   - 影响: 解决根本原因，减少后续问题")
        print()
        print("⚡ 优先级2: 优化跨交易所时间戳对齐")
        print("   - 问题: 时间戳差异超过阈值")
        print("   - 修复: 调整对齐算法和阈值设置")
        print("   - 影响: 减少套利机会丢失")
    else:
        print("🔥 优先级1: 优化跨交易所时间戳对齐")
        print("   - 问题: 时间戳差异超过阈值导致套利机会丢失")
        print("   - 修复: 调整对齐算法和阈值设置")
        print("   - 影响: 直接提升套利系统性能")
        print()
        print("⚡ 优先级2: 强化时间戳同步机制")
        print("   - 问题: 部分交易所时间戳同步不稳定")
        print("   - 修复: 优化同步重试和错误恢复")
        print("   - 影响: 提升系统稳定性")

if __name__ == "__main__":
    main()
