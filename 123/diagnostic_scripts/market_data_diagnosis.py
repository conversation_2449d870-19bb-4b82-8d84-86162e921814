#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场数据接收紧急诊断脚本
专门诊断为什么时间戳修复后没有市场数据被接收和处理
"""

import asyncio
import time
import logging
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class MarketDataDiagnostic:
    """市场数据接收诊断器"""
    
    def __init__(self):
        # 设置日志
        self.logger = logging.getLogger('MarketDataDiagnostic')
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    async def diagnose_market_data_issue(self):
        """诊断市场数据接收问题"""
        try:
            self.logger.info("🔍 开始诊断市场数据接收问题...")
            
            # 步骤1：检查OpportunityScanner的market_data状态
            await self._check_opportunity_scanner_data()
            
            # 步骤2：检查WebSocket回调注册
            await self._check_websocket_callbacks()
            
            # 步骤3：检查market_data更新机制
            await self._check_data_update_mechanism()
            
            # 步骤4：输出诊断结论
            await self._output_diagnosis_conclusion()
            
        except Exception as e:
            self.logger.error(f"❌ 诊断过程异常: {e}")
            import traceback
            traceback.print_exc()
    
    async def _check_opportunity_scanner_data(self):
        """检查OpportunityScanner的数据状态"""
        try:
            self.logger.info("📊 检查OpportunityScanner数据状态...")
            
            from core.opportunity_scanner import get_opportunity_scanner
            scanner = get_opportunity_scanner()
            
            if not scanner:
                self.logger.error("❌ 关键问题：OpportunityScanner实例不存在")
                return
            
            # 检查market_data
            if hasattr(scanner, 'market_data'):
                data_count = len(scanner.market_data)
                self.logger.info(f"📊 market_data条目数: {data_count}")
                
                if data_count == 0:
                    self.logger.error("🚨 关键发现：market_data为空 - 这是无差价显示的根本原因")
                    self.logger.error("🚨 市场数据未被接收，导致扫描器没有数据处理")
                else:
                    self.logger.info("✅ market_data有数据，让我分析具体内容...")
                    
                    # 分析数据内容
                    for key in list(scanner.market_data.keys())[:10]:  # 前10个
                        data = scanner.market_data[key]
                        self.logger.info(f"   📈 {key}: price={getattr(data, 'price', 'N/A')}, timestamp={getattr(data, 'timestamp', 'N/A')}")
                
                # 检查支持的交易对
                if hasattr(scanner, 'supported_symbols'):
                    symbols_count = len(scanner.supported_symbols)
                    self.logger.info(f"🎯 支持的交易对数: {symbols_count}")
                    self.logger.info(f"🎯 前5个交易对: {scanner.supported_symbols[:5]}")
                    
                    if symbols_count == 0:
                        self.logger.error("🚨 关键问题：supported_symbols为空")
                else:
                    self.logger.error("❌ 缺少supported_symbols属性")
            else:
                self.logger.error("❌ OpportunityScanner缺少market_data属性")
            
            # 检查running状态
            if hasattr(scanner, 'running'):
                running_status = scanner.running
                self.logger.info(f"🔄 扫描器运行状态: {running_status}")
                if not running_status:
                    self.logger.error("🚨 关键问题：扫描器未运行")
            else:
                self.logger.error("❌ 缺少running状态属性")
            
        except Exception as e:
            self.logger.error(f"❌ 检查OpportunityScanner数据失败: {e}")
    
    async def _check_websocket_callbacks(self):
        """检查WebSocket回调注册"""
        try:
            self.logger.info("🔗 检查WebSocket回调注册...")
            
            from websocket.ws_manager import get_ws_manager
            ws_manager = get_ws_manager()
            
            if not ws_manager:
                self.logger.error("❌ 关键问题：WebSocket管理器不存在")
                return
            
            # 检查回调注册
            if hasattr(ws_manager, 'callbacks'):
                callbacks = ws_manager.callbacks
                self.logger.info(f"📞 注册的回调类型: {list(callbacks.keys())}")
                
                # 检查market_data回调
                if 'market_data' in callbacks:
                    market_callbacks = callbacks['market_data']
                    callback_count = len(market_callbacks)
                    self.logger.info(f"📊 market_data回调数量: {callback_count}")
                    
                    if callback_count == 0:
                        self.logger.error("🚨 关键发现：没有market_data回调注册")
                        self.logger.error("🚨 这意味着WebSocket数据无法传递给OpportunityScanner")
                    else:
                        self.logger.info("✅ market_data回调已注册")
                        
                        # 检查回调函数是否指向OpportunityScanner
                        for i, callback in enumerate(market_callbacks):
                            callback_info = str(callback)
                            self.logger.info(f"   📞 回调{i+1}: {callback_info}")
                            
                            if 'OpportunityScanner' in callback_info or '_on_market_data' in callback_info:
                                self.logger.info("✅ 发现OpportunityScanner回调")
                            else:
                                self.logger.warning("⚠️ 未找到OpportunityScanner相关回调")
                else:
                    self.logger.error("🚨 关键问题：没有market_data回调类型")
            else:
                self.logger.error("❌ WebSocket管理器缺少callbacks属性")
            
            # 检查客户端连接状态
            if hasattr(ws_manager, 'clients'):
                clients = ws_manager.clients
                client_count = len(clients)
                self.logger.info(f"🌐 WebSocket客户端数量: {client_count}")
                
                if client_count == 0:
                    self.logger.error("🚨 关键问题：没有WebSocket客户端连接")
                else:
                    for client_key, client in clients.items():
                        try:
                            is_connected = False
                            if hasattr(client, 'is_connected') and callable(client.is_connected):
                                is_connected = client.is_connected()
                            
                            status_icon = "✅" if is_connected else "❌"
                            self.logger.info(f"   {status_icon} {client_key}: 连接状态={is_connected}")
                            
                            if not is_connected:
                                self.logger.error(f"🚨 {client_key} WebSocket连接断开")
                        except Exception as e:
                            self.logger.error(f"❌ 检查{client_key}连接状态失败: {e}")
            else:
                self.logger.error("❌ WebSocket管理器缺少clients属性")
            
        except Exception as e:
            self.logger.error(f"❌ 检查WebSocket回调失败: {e}")
    
    async def _check_data_update_mechanism(self):
        """检查数据更新机制"""
        try:
            self.logger.info("🔄 检查数据更新机制...")
            
            # 检查update_price方法
            from core.opportunity_scanner import get_opportunity_scanner
            scanner = get_opportunity_scanner()
            
            if scanner and hasattr(scanner, 'update_price'):
                self.logger.info("✅ OpportunityScanner有update_price方法")
                
                # 模拟调用update_price看看是否正常工作
                try:
                    test_time_before = time.time()
                    scanner.update_price('gate', 'ADA-USDT', 'spot', 0.5)  # 测试调用
                    test_time_after = time.time()
                    
                    # 检查是否有数据被添加
                    test_key = 'gate_spot_ADA-USDT'
                    if test_key in scanner.market_data:
                        self.logger.info("✅ update_price方法工作正常，数据已添加")
                        # 清理测试数据
                        del scanner.market_data[test_key]
                    else:
                        self.logger.error("🚨 关键问题：update_price方法不工作，数据未添加")
                        
                except Exception as e:
                    self.logger.error(f"❌ 测试update_price方法失败: {e}")
            else:
                self.logger.error("❌ OpportunityScanner缺少update_price方法")
            
            # 检查_on_market_data方法
            if scanner and hasattr(scanner, '_on_market_data'):
                self.logger.info("✅ OpportunityScanner有_on_market_data方法")
            else:
                self.logger.error("❌ OpportunityScanner缺少_on_market_data方法")
            
        except Exception as e:
            self.logger.error(f"❌ 检查数据更新机制失败: {e}") 
    
    async def _output_diagnosis_conclusion(self):
        """输出诊断结论"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("🏁 市场数据诊断结论")
            self.logger.info("=" * 80)
            
            self.logger.info("🎯 可能的问题原因（按优先级排序）：")
            self.logger.info("1. 🚨 OpportunityScanner的market_data为空")
            self.logger.info("2. 🚨 WebSocket回调未正确注册到OpportunityScanner")
            self.logger.info("3. 🚨 WebSocket客户端连接断开")
            self.logger.info("4. 🚨 update_price或_on_market_data方法不工作")
            
            self.logger.info("")
            self.logger.info("🔧 建议的修复步骤：")
            self.logger.info("1. 检查WebSocket连接是否正常建立")
            self.logger.info("2. 验证OpportunityScanner回调是否正确注册")
            self.logger.info("3. 确认市场数据接收流程是否完整")
            self.logger.info("4. 测试数据更新方法是否正常工作")
            
            self.logger.info("=" * 80)
            
        except Exception as e:
            self.logger.error(f"❌ 输出诊断结论失败: {e}")

async def main():
    """主函数"""
    diagnostic = MarketDataDiagnostic()
    await diagnostic.diagnose_market_data_issue()

if __name__ == "__main__":
    asyncio.run(main())