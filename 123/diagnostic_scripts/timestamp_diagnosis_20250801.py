#!/usr/bin/env python3
"""
🔥 时间戳问题精确诊断脚本 - 2025-08-01
专门诊断日志中显示的时间戳问题：
1. discarded_timestamp 浮点数问题
2. timestamp_age_ms 巨大值问题  
3. 时间戳单位不一致问题
4. 跨交易所时间戳同步问题
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any, List, Tuple

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_log_timestamps():
    """分析日志中的时间戳问题"""
    print("🔍 分析日志中的时间戳问题...")
    
    log_file = "123/logs/websocket_performance_20250801.log"
    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        return
    
    issues = []
    with open(log_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if "discarded_timestamp" in line:
                # 提取时间戳值
                try:
                    import re
                    match = re.search(r"discarded_timestamp['\"]:\s*([0-9.]+)", line)
                    if match:
                        timestamp_str = match.group(1)
                        timestamp_val = float(timestamp_str)
                        
                        # 检查是否为浮点数
                        if '.' in timestamp_str:
                            issues.append({
                                'line': line_num,
                                'issue': 'FLOAT_TIMESTAMP',
                                'value': timestamp_val,
                                'expected': int(timestamp_val),
                                'line_content': line.strip()
                            })
                        
                        # 检查时间戳年龄
                        age_match = re.search(r"timestamp_age_ms['\"]:\s*([0-9.]+)", line)
                        if age_match:
                            age_val = float(age_match.group(1))
                            if age_val > 10000:  # 超过10秒
                                issues.append({
                                    'line': line_num,
                                    'issue': 'EXCESSIVE_AGE',
                                    'age_ms': age_val,
                                    'timestamp': timestamp_val,
                                    'line_content': line.strip()
                                })
                except Exception as e:
                    print(f"⚠️ 解析第{line_num}行失败: {e}")
    
    print(f"📊 发现 {len(issues)} 个时间戳问题:")
    for issue in issues[:10]:  # 只显示前10个
        print(f"  - 第{issue['line']}行: {issue['issue']}")
        if issue['issue'] == 'FLOAT_TIMESTAMP':
            print(f"    浮点数: {issue['value']} -> 应该是: {issue['expected']}")
        elif issue['issue'] == 'EXCESSIVE_AGE':
            print(f"    年龄过大: {issue['age_ms']:.1f}ms, 时间戳: {issue['timestamp']}")
    
    return issues

def test_timestamp_functions():
    """测试时间戳处理函数"""
    print("\n🧪 测试时间戳处理函数...")
    
    try:
        from websocket.unified_timestamp_processor import (
            ensure_milliseconds_timestamp,
            calculate_data_age,
            get_timestamp_processor
        )
        
        current_time = time.time()
        current_ms = int(current_time * 1000)
        
        # 测试1: 浮点数时间戳标准化
        test_cases = [
            1754055467805.0,  # 浮点数毫秒
            1754055467.805,   # 浮点数秒
            1754055467805,    # 整数毫秒
            1754055467,       # 整数秒
        ]
        
        print("📋 时间戳标准化测试:")
        for i, ts in enumerate(test_cases, 1):
            result = ensure_milliseconds_timestamp(ts)
            print(f"  {i}. {ts} -> {result} (类型: {type(result).__name__})")
        
        # 测试2: 数据年龄计算
        print("\n📋 数据年龄计算测试:")
        test_timestamps = [
            current_ms,           # 当前时间
            current_ms - 1000,    # 1秒前
            current_ms - 30000,   # 30秒前
            current_ms + 1000,    # 1秒后（未来）
        ]
        
        for i, ts in enumerate(test_timestamps, 1):
            age = calculate_data_age(ts, current_time)
            print(f"  {i}. 时间戳 {ts}, 年龄: {age:.3f}秒")
        
        # 测试3: 跨交易所同步检查
        print("\n📋 跨交易所同步检查测试:")
        processor = get_timestamp_processor("gate")
        
        sync_test_cases = [
            (current_ms, current_ms + 100, "正常同步"),
            (current_ms, current_ms + 1000, "轻微不同步"),
            (current_ms, current_ms + 10000, "严重不同步"),
        ]
        
        for i, (ts1, ts2, desc) in enumerate(sync_test_cases, 1):
            is_synced, diff = processor.validate_cross_exchange_sync(
                ts1, ts2, "gate", "okx", max_diff_ms=800
            )
            print(f"  {i}. {desc}: 同步={is_synced}, 时间差={diff:.1f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_opportunity_scanner():
    """诊断OpportunityScanner中的时间戳问题"""
    print("\n🔍 诊断OpportunityScanner时间戳问题...")
    
    try:
        # 模拟OpportunityScanner中的时间戳计算
        current_time = int(time.time() * 1000)  # 毫秒级
        spot_timestamp = int(time.time() * 1000) - 200  # 200ms前
        futures_timestamp = int(time.time() * 1000) - 300  # 300ms前
        
        print(f"📊 模拟数据:")
        print(f"  current_time: {current_time} (毫秒)")
        print(f"  spot_timestamp: {spot_timestamp} (毫秒)")
        print(f"  futures_timestamp: {futures_timestamp} (毫秒)")
        
        # 测试错误的计算方式（修复前）
        print("\n❌ 错误计算方式（修复前）:")
        wrong_age_spot = current_time - spot_timestamp  # 毫秒 - 毫秒
        wrong_age_futures = current_time - futures_timestamp
        print(f"  data_age_spot: {wrong_age_spot}ms")
        print(f"  data_age_futures: {wrong_age_futures}ms")
        
        # 测试正确的计算方式（修复后）
        print("\n✅ 正确计算方式（修复后）:")
        from websocket.unified_timestamp_processor import calculate_data_age
        
        current_time_seconds = current_time / 1000  # 转换为秒
        correct_age_spot_seconds = calculate_data_age(spot_timestamp, current_time_seconds)
        correct_age_futures_seconds = calculate_data_age(futures_timestamp, current_time_seconds)
        correct_age_spot = correct_age_spot_seconds * 1000  # 转换为毫秒
        correct_age_futures = correct_age_futures_seconds * 1000
        
        print(f"  data_age_spot: {correct_age_spot:.1f}ms")
        print(f"  data_age_futures: {correct_age_futures:.1f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔥 时间戳问题精确诊断 - 2025-08-01")
    print("=" * 60)
    
    # 1. 分析日志中的时间戳问题
    log_issues = analyze_log_timestamps()
    
    # 2. 测试时间戳处理函数
    functions_ok = test_timestamp_functions()
    
    # 3. 诊断OpportunityScanner问题
    scanner_ok = diagnose_opportunity_scanner()
    
    # 4. 生成诊断报告
    print("\n📋 诊断报告:")
    print(f"  日志问题数量: {len(log_issues) if log_issues else 0}")
    print(f"  时间戳函数测试: {'✅ 通过' if functions_ok else '❌ 失败'}")
    print(f"  OpportunityScanner诊断: {'✅ 通过' if scanner_ok else '❌ 失败'}")
    
    # 5. 生成修复建议
    print("\n🔧 修复建议:")
    if log_issues:
        float_issues = [i for i in log_issues if i['issue'] == 'FLOAT_TIMESTAMP']
        age_issues = [i for i in log_issues if i['issue'] == 'EXCESSIVE_AGE']
        
        if float_issues:
            print(f"  1. 修复浮点数时间戳问题 ({len(float_issues)}个)")
            print("     - 在unified_timestamp_processor.py中确保返回整数时间戳")
        
        if age_issues:
            print(f"  2. 修复时间戳年龄过大问题 ({len(age_issues)}个)")
            print("     - 检查时间戳单位转换逻辑")
            print("     - 确保使用统一的毫秒时间戳")
    
    if not functions_ok:
        print("  3. 修复时间戳处理函数")
        print("     - 检查ensure_milliseconds_timestamp函数")
        print("     - 检查calculate_data_age函数")
    
    if not scanner_ok:
        print("  4. 修复OpportunityScanner时间戳计算")
        print("     - 使用统一的时间戳处理函数")
        print("     - 确保时间戳单位一致性")

if __name__ == "__main__":
    main()
