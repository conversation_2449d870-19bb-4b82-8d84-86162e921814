#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
差价显示问题精确诊断脚本
诊断为什么时间戳修复后系统没有差价显示
"""

import asyncio
import time
import sys
import os
import json
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from config.settings import get_config

class SpreadDisplayDiagnostic:
    """差价显示问题诊断器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.config = get_config()
        self.diagnosis_results = {
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'issues_found': [],
            'recommendations': []
        }
    
    async def run_comprehensive_diagnosis(self):
        """运行综合诊断"""
        try:
            self.logger.info("🔍 开始差价显示问题综合诊断...")
            
            # 1. 检查WebSocket数据流状态
            await self._check_websocket_data_flow()
            
            # 2. 检查OpportunityScanner状态
            await self._check_opportunity_scanner_status()
            
            # 3. 检查market_data存储状态
            await self._check_market_data_storage()
            
            # 4. 检查差价计算逻辑
            await self._check_spread_calculation_logic()
            
            # 5. 检查日志输出机制
            await self._check_logging_mechanism()
            
            # 6. 检查时间戳相关问题
            await self._check_timestamp_issues()
            
            # 7. 生成诊断报告
            await self._generate_diagnosis_report()
            
        except Exception as e:
            self.logger.error(f"❌ 诊断过程异常: {e}")
            import traceback
            traceback.print_exc()
    
    async def _check_websocket_data_flow(self):
        """检查WebSocket数据流状态"""
        try:
            self.logger.info("📡 检查WebSocket数据流状态...")
            
            check_result = {
                'test_name': 'WebSocket数据流检查',
                'status': 'unknown',
                'details': {},
                'issues': []
            }
            
            # 检查WebSocket管理器
            try:
                from websocket.ws_manager import get_ws_manager
                ws_manager = get_ws_manager()
                
                if ws_manager:
                    check_result['details']['ws_manager_available'] = True
                    check_result['details']['client_count'] = len(ws_manager.clients) if hasattr(ws_manager, 'clients') else 0
                    
                    # 检查每个交易所的连接状态
                    connection_status = {}
                    for exchange in ['gate', 'bybit', 'okx']:
                        try:
                            client_key = f"{exchange}_spot"  # 检查现货连接
                            if hasattr(ws_manager, 'clients') and client_key in ws_manager.clients:
                                client = ws_manager.clients[client_key]
                                if hasattr(client, 'is_connected') and callable(client.is_connected):
                                    connection_status[exchange] = client.is_connected()
                                else:
                                    connection_status[exchange] = 'unknown'
                            else:
                                connection_status[exchange] = False
                        except Exception as e:
                            connection_status[exchange] = f'error: {e}'
                    
                    check_result['details']['connection_status'] = connection_status
                    
                    # 检查回调注册
                    if hasattr(ws_manager, 'callbacks'):
                        check_result['details']['callback_types'] = list(ws_manager.callbacks.keys())
                        check_result['details']['market_data_callbacks'] = len(ws_manager.callbacks.get('market_data', []))
                    
                    # 判断整体状态
                    connected_count = sum(1 for status in connection_status.values() if status is True)
                    if connected_count >= 2:
                        check_result['status'] = 'healthy'
                    elif connected_count >= 1:
                        check_result['status'] = 'partial'
                        check_result['issues'].append(f"只有{connected_count}/3个交易所连接正常")
                    else:
                        check_result['status'] = 'failed'
                        check_result['issues'].append("所有交易所WebSocket连接失败")
                else:
                    check_result['status'] = 'failed'
                    check_result['issues'].append("WebSocket管理器不可用")
                    check_result['details']['ws_manager_available'] = False
                    
            except Exception as e:
                check_result['status'] = 'error'
                check_result['issues'].append(f"WebSocket检查异常: {e}")
            
            self.diagnosis_results['checks']['websocket_data_flow'] = check_result
            self.logger.info(f"📡 WebSocket数据流检查完成: {check_result['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket数据流检查失败: {e}")
    
    async def _check_opportunity_scanner_status(self):
        """检查OpportunityScanner状态"""
        try:
            self.logger.info("🔍 检查OpportunityScanner状态...")
            
            check_result = {
                'test_name': 'OpportunityScanner状态检查',
                'status': 'unknown',
                'details': {},
                'issues': []
            }
            
            try:
                from core.opportunity_scanner import get_opportunity_scanner
                scanner = get_opportunity_scanner()
                
                if scanner:
                    check_result['details']['scanner_available'] = True
                    
                    # 检查基本状态
                    if hasattr(scanner, 'running'):
                        check_result['details']['running'] = scanner.running
                    
                    if hasattr(scanner, 'market_data'):
                        data_count = len(scanner.market_data)
                        check_result['details']['market_data_count'] = data_count
                        
                        # 详细分析市场数据
                        exchange_data = {}
                        for key in scanner.market_data.keys():
                            parts = key.split('_')
                            if len(parts) >= 2:
                                exchange = parts[0]
                                market_type = parts[1]
                                
                                if exchange not in exchange_data:
                                    exchange_data[exchange] = {}
                                if market_type not in exchange_data[exchange]:
                                    exchange_data[exchange][market_type] = 0
                                exchange_data[exchange][market_type] += 1
                        
                        check_result['details']['exchange_data_breakdown'] = exchange_data
                        
                        # 关键检查：Bybit期货数据
                        bybit_futures_count = sum(1 for key in scanner.market_data.keys() if 'bybit_futures' in key)
                        check_result['details']['bybit_futures_data_count'] = bybit_futures_count
                        
                        if bybit_futures_count == 0:
                            check_result['issues'].append("🚨 关键问题：Bybit期货数据为0，这是导致无差价的主要原因")
                    
                    if hasattr(scanner, 'available_combinations'):
                        check_result['details']['available_combinations'] = len(scanner.available_combinations)
                        combination_names = [combo.name for combo in scanner.available_combinations] if hasattr(scanner, 'available_combinations') else []
                        check_result['details']['combination_names'] = combination_names
                    
                    if hasattr(scanner, 'current_opportunities'):
                        check_result['details']['current_opportunities'] = len(scanner.current_opportunities)
                    
                    # 检查支持的交易对
                    if hasattr(scanner, 'supported_symbols'):
                        check_result['details']['supported_symbols_count'] = len(scanner.supported_symbols)
                        check_result['details']['supported_symbols'] = scanner.supported_symbols[:5]  # 显示前5个
                    
                    # 判断状态
                    if check_result['details'].get('market_data_count', 0) > 0:
                        if check_result['details'].get('bybit_futures_data_count', 0) > 0:
                            check_result['status'] = 'healthy'
                        else:
                            check_result['status'] = 'critical'  # Bybit期货数据缺失是关键问题
                    else:
                        check_result['status'] = 'failed'
                        check_result['issues'].append("完全没有市场数据")
                    
                else:
                    check_result['status'] = 'failed'
                    check_result['issues'].append("OpportunityScanner不可用")
                    check_result['details']['scanner_available'] = False
                    
            except Exception as e:
                check_result['status'] = 'error'
                check_result['issues'].append(f"OpportunityScanner检查异常: {e}")
            
            self.diagnosis_results['checks']['opportunity_scanner'] = check_result
            self.logger.info(f"🔍 OpportunityScanner检查完成: {check_result['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ OpportunityScanner检查失败: {e}")
    
    async def _check_market_data_storage(self):
        """检查market_data存储状态"""
        try:
            self.logger.info("📊 检查market_data存储状态...")
            
            check_result = {
                'test_name': 'market_data存储检查',
                'status': 'unknown',
                'details': {},
                'issues': []
            }
            
            try:
                from core.opportunity_scanner import get_opportunity_scanner
                scanner = get_opportunity_scanner()
                
                if scanner and hasattr(scanner, 'market_data'):
                    market_data = scanner.market_data
                    
                    check_result['details']['total_entries'] = len(market_data)
                    
                    if len(market_data) > 0:
                        # 分析数据结构
                        sample_key = list(market_data.keys())[0]
                        sample_data = market_data[sample_key]
                        
                        check_result['details']['sample_key'] = sample_key
                        check_result['details']['sample_data_type'] = type(sample_data).__name__
                        
                        # 检查数据字段
                        if hasattr(sample_data, 'exchange'):
                            check_result['details']['has_exchange_field'] = True
                        if hasattr(sample_data, 'symbol'):
                            check_result['details']['has_symbol_field'] = True
                        if hasattr(sample_data, 'price'):
                            check_result['details']['has_price_field'] = True
                            check_result['details']['sample_price'] = sample_data.price
                        if hasattr(sample_data, 'timestamp'):
                            check_result['details']['has_timestamp_field'] = True
                            check_result['details']['sample_timestamp'] = sample_data.timestamp
                        if hasattr(sample_data, 'orderbook'):
                            check_result['details']['has_orderbook_field'] = True
                            orderbook = sample_data.orderbook
                            if orderbook:
                                check_result['details']['orderbook_has_data'] = True
                                check_result['details']['orderbook_asks_count'] = len(orderbook.get('asks', []))
                                check_result['details']['orderbook_bids_count'] = len(orderbook.get('bids', []))
                            else:
                                check_result['details']['orderbook_has_data'] = False
                                check_result['issues'].append("订单簿数据为空")
                    
                        # 检查数据新鲜度
                        current_time = time.time()
                        fresh_data_count = 0
                        stale_data_count = 0
                        
                        for key, data in list(market_data.items())[:10]:  # 检查前10个数据
                            if hasattr(data, 'timestamp'):
                                # 使用统一时间戳处理器
                                try:
                                    from websocket.unified_timestamp_processor import calculate_data_age
                                    data_age = calculate_data_age(data.timestamp, current_time)
                                    if data_age < 10:  # 10秒内算新鲜
                                        fresh_data_count += 1
                                    else:
                                        stale_data_count += 1
                                except Exception as e:
                                    check_result['issues'].append(f"时间戳计算异常: {e}")
                        
                        check_result['details']['fresh_data_count'] = fresh_data_count
                        check_result['details']['stale_data_count'] = stale_data_count
                        
                        # 状态判断
                        if len(market_data) > 5 and fresh_data_count > stale_data_count:
                            check_result['status'] = 'healthy'
                        elif len(market_data) > 0:
                            check_result['status'] = 'partial'
                            if stale_data_count > fresh_data_count:
                                check_result['issues'].append("大部分数据过期")
                        else:
                            check_result['status'] = 'failed'
                            check_result['issues'].append("没有市场数据")
                    else:
                        check_result['status'] = 'failed'
                        check_result['issues'].append("market_data为空")
                else:
                    check_result['status'] = 'failed'
                    check_result['issues'].append("无法访问market_data")
            
            except Exception as e:
                check_result['status'] = 'error'
                check_result['issues'].append(f"market_data检查异常: {e}")
            
            self.diagnosis_results['checks']['market_data_storage'] = check_result
            self.logger.info(f"📊 market_data存储检查完成: {check_result['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ market_data存储检查失败: {e}")
    
    async def _check_spread_calculation_logic(self):
        """检查差价计算逻辑"""
        try:
            self.logger.info("🧮 检查差价计算逻辑...")
            
            check_result = {
                'test_name': '差价计算逻辑检查',
                'status': 'unknown',
                'details': {},
                'issues': []
            }
            
            try:
                # 检查统一差价计算器
                from core.unified_order_spread_calculator import get_order_spread_calculator
                calculator = get_order_spread_calculator()
                
                if calculator:
                    check_result['details']['calculator_available'] = True
                    
                    # 创建测试数据
                    test_spot_orderbook = {
                        'asks': [[1.0, 100], [1.01, 200]],
                        'bids': [[0.99, 100], [0.98, 200]]
                    }
                    
                    test_futures_orderbook = {
                        'asks': [[1.02, 100], [1.03, 200]],
                        'bids': [[1.01, 100], [1.00, 200]]
                    }
                    
                    # 测试差价计算
                    try:
                        result = calculator.calculate_order_based_spread(
                            test_spot_orderbook, 
                            test_futures_orderbook, 
                            100.0, 
                            "opening"
                        )
                        
                        if result:
                            check_result['details']['test_calculation_success'] = True
                            check_result['details']['test_spread'] = result.executable_spread
                            check_result['details']['test_spot_price'] = result.spot_execution_price
                            check_result['details']['test_futures_price'] = result.futures_execution_price
                            check_result['status'] = 'healthy'
                        else:
                            check_result['status'] = 'failed'
                            check_result['issues'].append("差价计算返回None")
                    
                    except Exception as e:
                        check_result['status'] = 'error'
                        check_result['issues'].append(f"差价计算异常: {e}")
                else:
                    check_result['status'] = 'failed'
                    check_result['issues'].append("差价计算器不可用")
            
            except Exception as e:
                check_result['status'] = 'error'
                check_result['issues'].append(f"差价计算逻辑检查异常: {e}")
            
            self.diagnosis_results['checks']['spread_calculation'] = check_result
            self.logger.info(f"🧮 差价计算逻辑检查完成: {check_result['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ 差价计算逻辑检查失败: {e}")
    
    async def _check_logging_mechanism(self):
        """检查日志输出机制"""
        try:
            self.logger.info("📝 检查日志输出机制...")
            
            check_result = {
                'test_name': '日志输出机制检查',
                'status': 'unknown',
                'details': {},
                'issues': []
            }
            
            try:
                # 检查websocket_prices.log文件
                log_file_path = project_root / "logs" / "websocket_prices.log"
                
                if log_file_path.exists():
                    check_result['details']['log_file_exists'] = True
                    
                    # 检查文件大小
                    file_size = log_file_path.stat().st_size
                    check_result['details']['log_file_size'] = file_size
                    
                    # 检查最近修改时间
                    mtime = log_file_path.stat().st_mtime
                    time_since_modified = time.time() - mtime
                    check_result['details']['seconds_since_last_modified'] = time_since_modified
                    
                    # 读取最后几行
                    try:
                        with open(log_file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            last_lines = lines[-10:] if len(lines) > 10 else lines
                            check_result['details']['last_lines_count'] = len(last_lines)
                            check_result['details']['last_lines'] = [line.strip() for line in last_lines]
                            
                            # 检查是否有实际的差价数据
                            spread_lines = [line for line in last_lines if '差价' in line and '%' in line]
                            check_result['details']['spread_lines_count'] = len(spread_lines)
                            
                            if len(spread_lines) == 0:
                                check_result['issues'].append("🚨 关键问题：日志文件中没有差价数据输出")
                                check_result['status'] = 'critical'
                            else:
                                check_result['status'] = 'healthy'
                    
                    except Exception as e:
                        check_result['issues'].append(f"读取日志文件失败: {e}")
                else:
                    check_result['details']['log_file_exists'] = False
                    check_result['issues'].append("websocket_prices.log文件不存在")
                    check_result['status'] = 'failed'
                
                # 检查日志器配置
                import logging
                websocket_logger = logging.getLogger('websocket_prices_only')
                if websocket_logger:
                    check_result['details']['websocket_logger_exists'] = True
                    check_result['details']['websocket_logger_level'] = websocket_logger.level
                    check_result['details']['websocket_logger_handlers'] = len(websocket_logger.handlers)
                else:
                    check_result['issues'].append("websocket_prices_only日志器不存在")
            
            except Exception as e:
                check_result['status'] = 'error'
                check_result['issues'].append(f"日志机制检查异常: {e}")
            
            self.diagnosis_results['checks']['logging_mechanism'] = check_result
            self.logger.info(f"📝 日志输出机制检查完成: {check_result['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ 日志输出机制检查失败: {e}")
    
    async def _check_timestamp_issues(self):
        """检查时间戳相关问题"""
        try:
            self.logger.info("⏰ 检查时间戳相关问题...")
            
            check_result = {
                'test_name': '时间戳问题检查',
                'status': 'unknown',
                'details': {},
                'issues': []
            }
            
            try:
                # 检查统一时间戳处理器
                from websocket.unified_timestamp_processor import get_synced_timestamp, calculate_data_age
                
                # 测试时间戳生成
                test_timestamp = get_synced_timestamp("system", None)
                check_result['details']['test_timestamp'] = test_timestamp
                check_result['details']['timestamp_type'] = type(test_timestamp).__name__
                
                # 测试数据年龄计算
                current_time = time.time()
                age = calculate_data_age(test_timestamp, current_time)
                check_result['details']['calculated_age'] = age
                
                if age < 1.0:  # 应该接近0
                    check_result['status'] = 'healthy'
                else:
                    check_result['status'] = 'warning'
                    check_result['issues'].append(f"时间戳计算可能有问题，年龄={age}秒")
                
                # 检查跨交易所时间戳同步
                from websocket.unified_timestamp_processor import get_timestamp_processor
                processor = get_timestamp_processor("gate")
                
                test_timestamp_1 = test_timestamp
                test_timestamp_2 = test_timestamp + 100  # 100ms差异
                
                is_synced, time_diff = processor.validate_cross_exchange_sync(
                    test_timestamp_1, test_timestamp_2, "gate", "bybit", max_diff_ms=800
                )
                
                check_result['details']['sync_test_result'] = is_synced
                check_result['details']['sync_time_diff'] = time_diff
            
            except Exception as e:
                check_result['status'] = 'error'
                check_result['issues'].append(f"时间戳检查异常: {e}")
            
            self.diagnosis_results['checks']['timestamp_issues'] = check_result
            self.logger.info(f"⏰ 时间戳检查完成: {check_result['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ 时间戳检查失败: {e}")
    
    async def _generate_diagnosis_report(self):
        """生成诊断报告"""
        try:
            self.logger.info("📋 生成诊断报告...")
            
            # 分析所有检查结果
            total_checks = len(self.diagnosis_results['checks'])
            healthy_checks = sum(1 for check in self.diagnosis_results['checks'].values() if check['status'] == 'healthy')
            critical_checks = sum(1 for check in self.diagnosis_results['checks'].values() if check['status'] == 'critical')
            failed_checks = sum(1 for check in self.diagnosis_results['checks'].values() if check['status'] == 'failed')
            
            # 总结问题
            all_issues = []
            for check in self.diagnosis_results['checks'].values():
                all_issues.extend(check.get('issues', []))
            
            self.diagnosis_results['summary'] = {
                'total_checks': total_checks,
                'healthy_checks': healthy_checks,
                'critical_checks': critical_checks,
                'failed_checks': failed_checks,
                'total_issues': len(all_issues),
                'critical_issues': [issue for issue in all_issues if '🚨 关键问题' in issue]
            }
            
            # 生成建议
            if critical_checks > 0:
                self.diagnosis_results['recommendations'].append("🚨 发现关键问题，需要立即修复")
            
            if 'Bybit期货数据为0' in str(all_issues):
                self.diagnosis_results['recommendations'].append("🎯 优先修复：Bybit期货WebSocket数据接收问题")
            
            if '日志文件中没有差价数据输出' in str(all_issues):
                self.diagnosis_results['recommendations'].append("📝 检查日志输出逻辑，确保差价计算结果正确记录")
            
            # 保存报告
            report_file = project_root / "diagnostic_results" / f"spread_display_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_file.parent.mkdir(exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.diagnosis_results, f, ensure_ascii=False, indent=2)
            
            # 打印摘要
            self.logger.info("🏁 差价显示问题诊断完成")
            self.logger.info(f"📊 检查统计: {healthy_checks}/{total_checks}个正常, {critical_checks}个关键问题, {failed_checks}个失败")
            self.logger.info(f"🔍 发现问题: {len(all_issues)}个")
            
            for issue in self.diagnosis_results['summary']['critical_issues']:
                self.logger.error(issue)
            
            for recommendation in self.diagnosis_results['recommendations']:
                self.logger.info(f"💡 建议: {recommendation}")
            
            self.logger.info(f"📄 详细报告: {report_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 生成诊断报告失败: {e}")

async def main():
    """主函数"""
    diagnostic = SpreadDisplayDiagnostic()
    await diagnostic.run_comprehensive_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())