#!/usr/bin/env python3
"""
权威验证测试 - 确保修复的真实性和完整性
验证是否引入新问题，确保完美修复
"""

import asyncio
import sys
import os
import time
import json
import traceback
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AuthoritativeVerification:
    """权威验证测试"""
    
    def __init__(self):
        self.verification_results = {
            "timestamp": int(time.time()),
            "tests": {},
            "issues_found": [],
            "api_calls_made": 0,
            "real_api_responses": []
        }
    
    async def verify_real_api_calls(self):
        """验证真实API调用"""
        print("🔍 验证真实API调用...")
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            import aiohttp
            
            # 直接验证API URL是否真实可访问
            api_urls = {
                "gate": "https://api.gateio.ws/api/v4/spot/time",
                "bybit": "https://api.bybit.com/v5/market/time", 
                "okx": "https://www.okx.com/api/v5/public/time"
            }
            
            real_responses = []
            
            for exchange, url in api_urls.items():
                print(f"  📡 直接测试 {exchange.upper()} API: {url}")
                
                try:
                    async with aiohttp.ClientSession() as session:
                        start_time = time.time()
                        async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                            api_latency = (time.time() - start_time) * 1000
                            
                            if response.status == 200:
                                data = await response.json()
                                real_responses.append({
                                    "exchange": exchange,
                                    "url": url,
                                    "status": response.status,
                                    "latency_ms": api_latency,
                                    "response_data": data,
                                    "real_api": True
                                })
                                print(f"    ✅ {exchange.upper()}: 真实API响应成功 (延迟{api_latency:.1f}ms)")
                                self.verification_results["api_calls_made"] += 1
                            else:
                                print(f"    ❌ {exchange.upper()}: API响应错误 {response.status}")
                                real_responses.append({
                                    "exchange": exchange,
                                    "url": url,
                                    "status": response.status,
                                    "latency_ms": api_latency,
                                    "error": f"HTTP {response.status}",
                                    "real_api": True
                                })
                                
                except Exception as e:
                    print(f"    ❌ {exchange.upper()}: API调用异常 - {e}")
                    real_responses.append({
                        "exchange": exchange,
                        "url": url,
                        "error": str(e),
                        "real_api": True
                    })
            
            self.verification_results["real_api_responses"] = real_responses
            
            # 验证处理器是否使用真实API
            print("  🔍 验证处理器API调用...")
            
            for exchange in ["gate", "bybit", "okx"]:
                processor = get_timestamp_processor(exchange)
                
                # 检查API URL配置
                expected_url = api_urls[exchange]
                actual_url = processor.time_api_urls.get(exchange)
                
                if actual_url == expected_url:
                    print(f"    ✅ {exchange.upper()}: 使用真实API URL")
                else:
                    print(f"    ❌ {exchange.upper()}: API URL不匹配")
                    self.verification_results["issues_found"].append({
                        "type": "API_URL_MISMATCH",
                        "exchange": exchange,
                        "expected": expected_url,
                        "actual": actual_url
                    })
                
                # 执行真实同步并验证
                print(f"    🔄 执行 {exchange.upper()} 真实时间同步...")
                start_time = time.time()
                sync_success = await processor.sync_time(force=True)
                sync_duration = time.time() - start_time
                
                if sync_success:
                    status = processor.get_sync_status()
                    print(f"      ✅ 同步成功: 偏移={status['time_offset_ms']}ms, 耗时={sync_duration:.2f}s")
                    self.verification_results["api_calls_made"] += 1
                else:
                    print(f"      ❌ 同步失败: 耗时={sync_duration:.2f}s")
                    self.verification_results["issues_found"].append({
                        "type": "SYNC_FAILURE",
                        "exchange": exchange,
                        "duration": sync_duration
                    })
            
            return len(self.verification_results["issues_found"]) == 0
            
        except Exception as e:
            print(f"❌ 真实API调用验证失败: {e}")
            self.verification_results["issues_found"].append({
                "type": "API_VERIFICATION_ERROR",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return False
    
    async def verify_no_new_issues(self):
        """验证没有引入新问题"""
        print("🔍 验证没有引入新问题...")
        
        try:
            # 检查原有接口兼容性
            print("  📋 检查接口兼容性...")
            
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient  
            from websocket.okx_ws import OKXWebSocketClient
            
            # 验证WebSocket客户端仍然可以正常创建
            clients = [
                ("gate", GateWebSocketClient, "spot"),
                ("bybit", BybitWebSocketClient, "spot"),
                ("okx", OKXWebSocketClient, "spot")
            ]
            
            for exchange_name, client_class, market_type in clients:
                try:
                    client = client_class(market_type)
                    
                    # 验证必要属性存在
                    required_attrs = ['exchange_name', 'market_type']
                    for attr in required_attrs:
                        if not hasattr(client, attr):
                            print(f"    ❌ {exchange_name.upper()}: 缺少属性 {attr}")
                            self.verification_results["issues_found"].append({
                                "type": "MISSING_ATTRIBUTE",
                                "exchange": exchange_name,
                                "attribute": attr
                            })
                        else:
                            print(f"    ✅ {exchange_name.upper()}: 属性 {attr} 存在")
                    
                except Exception as e:
                    print(f"    ❌ {exchange_name.upper()}: 客户端创建失败 - {e}")
                    self.verification_results["issues_found"].append({
                        "type": "CLIENT_CREATION_ERROR",
                        "exchange": exchange_name,
                        "error": str(e)
                    })
            
            # 检查WebSocket管理器
            print("  📋 检查WebSocket管理器...")
            
            from websocket.ws_manager import WebSocketManager
            
            try:
                ws_manager = WebSocketManager()
                
                # 验证集中式同步方法存在
                if hasattr(ws_manager, '_centralized_time_sync'):
                    print("    ✅ 集中式同步方法存在")
                else:
                    print("    ❌ 集中式同步方法缺失")
                    self.verification_results["issues_found"].append({
                        "type": "MISSING_METHOD",
                        "class": "WebSocketManager",
                        "method": "_centralized_time_sync"
                    })
                
                # 验证原有方法仍然存在
                required_methods = ['start', 'stop']
                for method in required_methods:
                    if hasattr(ws_manager, method):
                        print(f"    ✅ 方法 {method} 存在")
                    else:
                        print(f"    ❌ 方法 {method} 缺失")
                        self.verification_results["issues_found"].append({
                            "type": "MISSING_METHOD",
                            "class": "WebSocketManager", 
                            "method": method
                        })
                        
            except Exception as e:
                print(f"    ❌ WebSocket管理器检查失败 - {e}")
                self.verification_results["issues_found"].append({
                    "type": "WS_MANAGER_ERROR",
                    "error": str(e)
                })
            
            # 检查统一时间戳处理器
            print("  📋 检查统一时间戳处理器...")
            
            from websocket.unified_timestamp_processor import get_timestamp_processor, get_synced_timestamp
            
            for exchange in ["gate", "bybit", "okx"]:
                try:
                    # 验证处理器获取
                    processor = get_timestamp_processor(exchange)
                    print(f"    ✅ {exchange.upper()}: 处理器获取成功")
                    
                    # 验证全局接口
                    timestamp = get_synced_timestamp(exchange)
                    if isinstance(timestamp, int) and timestamp > 1700000000000:
                        print(f"    ✅ {exchange.upper()}: 全局接口正常")
                    else:
                        print(f"    ❌ {exchange.upper()}: 全局接口异常")
                        self.verification_results["issues_found"].append({
                            "type": "GLOBAL_INTERFACE_ERROR",
                            "exchange": exchange,
                            "timestamp": timestamp
                        })
                        
                except Exception as e:
                    print(f"    ❌ {exchange.upper()}: 处理器检查失败 - {e}")
                    self.verification_results["issues_found"].append({
                        "type": "PROCESSOR_ERROR",
                        "exchange": exchange,
                        "error": str(e)
                    })
            
            return len(self.verification_results["issues_found"]) == 0
            
        except Exception as e:
            print(f"❌ 新问题验证失败: {e}")
            self.verification_results["issues_found"].append({
                "type": "NEW_ISSUES_CHECK_ERROR",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return False
    
    async def verify_performance_impact(self):
        """验证性能影响"""
        print("🔍 验证性能影响...")
        
        try:
            import psutil
            import gc
            
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            print(f"  📊 初始内存使用: {initial_memory:.1f}MB")
            
            # 执行大量操作测试性能
            from websocket.unified_timestamp_processor import get_synced_timestamp
            
            start_time = time.time()
            
            # 高频调用测试
            for i in range(1000):
                for exchange in ["gate", "bybit", "okx"]:
                    timestamp = get_synced_timestamp(exchange)
            
            duration = time.time() - start_time
            
            gc.collect()
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"  📊 性能测试结果:")
            print(f"    - 3000次调用耗时: {duration:.2f}s")
            print(f"    - 平均每次调用: {(duration/3000)*1000:.2f}ms")
            print(f"    - 内存增长: {memory_increase:.1f}MB")
            
            # 性能阈值检查
            performance_ok = True
            
            if duration > 30.0:  # 30秒阈值
                print("    ❌ 性能测试超时")
                self.verification_results["issues_found"].append({
                    "type": "PERFORMANCE_TIMEOUT",
                    "duration": duration,
                    "threshold": 30.0
                })
                performance_ok = False
            
            if memory_increase > 100:  # 100MB阈值
                print("    ❌ 内存增长过多")
                self.verification_results["issues_found"].append({
                    "type": "MEMORY_LEAK",
                    "memory_increase": memory_increase,
                    "threshold": 100
                })
                performance_ok = False
            
            if performance_ok:
                print("    ✅ 性能表现正常")
            
            return performance_ok
            
        except Exception as e:
            print(f"❌ 性能验证失败: {e}")
            self.verification_results["issues_found"].append({
                "type": "PERFORMANCE_CHECK_ERROR",
                "error": str(e)
            })
            return False
    
    async def run_authoritative_verification(self):
        """运行权威验证"""
        print("🏛️ 启动权威验证测试")
        print("=" * 80)
        
        tests = [
            ("真实API调用验证", self.verify_real_api_calls),
            ("新问题检查", self.verify_no_new_issues),
            ("性能影响验证", self.verify_performance_impact)
        ]
        
        passed_tests = 0
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}...")
            try:
                result = await test_func()
                if result:
                    print(f"✅ {test_name}: 通过")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name}: 失败")
            except Exception as e:
                print(f"❌ {test_name}: 异常 - {e}")
        
        # 生成权威报告
        print("\n" + "=" * 80)
        print("📊 权威验证报告")
        print("=" * 80)
        
        print(f"通过测试: {passed_tests}/{len(tests)}")
        print(f"发现问题: {len(self.verification_results['issues_found'])}")
        print(f"真实API调用: {self.verification_results['api_calls_made']}")
        
        if self.verification_results["issues_found"]:
            print("\n⚠️ 发现的问题:")
            for i, issue in enumerate(self.verification_results["issues_found"], 1):
                print(f"  {i}. {issue['type']}: {issue}")
        
        # 最终结论
        is_authoritative = (passed_tests == len(tests) and 
                          len(self.verification_results["issues_found"]) == 0 and
                          self.verification_results["api_calls_made"] > 0)
        
        print("\n" + "=" * 80)
        if is_authoritative:
            print("🏆 权威验证结论: 修复完美，测试权威")
            print("✅ 确认使用真实API调用")
            print("✅ 确认没有引入新问题") 
            print("✅ 确认性能表现良好")
            print("✅ 可以放心部署到生产环境")
        else:
            print("❌ 权威验证结论: 存在问题，需要进一步修复")
            print("⚠️ 不建议立即部署到生产环境")
        
        # 保存验证结果
        output_file = "diagnostic_scripts/authoritative_verification_result.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.verification_results, f, indent=2, ensure_ascii=False, default=str)
            print(f"\n✅ 权威验证结果已保存到: {output_file}")
        except Exception as e:
            print(f"\n⚠️ 保存验证结果失败: {e}")
        
        return is_authoritative

async def main():
    """主函数"""
    verifier = AuthoritativeVerification()
    success = await verifier.run_authoritative_verification()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
