#!/usr/bin/env python3
"""
机构级别测试 - 第三段：生产测试
真实订单簿、真实API响应、网络波动模拟、多任务并发压力、极限滑点与稀有差价场景回放
确保部署到实盘零失误
"""

import asyncio
import sys
import os
import time
import json
import random
from typing import Dict, Any, List, Optional
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstitutionalGradeTestStage3:
    """机构级别测试 - 第三段：生产测试"""
    
    def __init__(self):
        self.test_results = {
            "stage": "生产测试",
            "timestamp": int(time.time()),
            "tests": {},
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "success_rate": 0.0
            }
        }
    
    async def test_real_api_responses(self):
        """测试真实API响应处理"""
        print("🔍 测试真实API响应处理...")
        
        test_name = "real_api_responses"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            exchanges = ["gate", "bybit", "okx"]
            
            # 测试真实API时间同步
            print("  📋 测试真实API时间同步")
            
            for exchange in exchanges:
                processor = get_timestamp_processor(exchange)
                
                # 执行真实API调用
                start_time = time.time()
                sync_success = await processor.sync_time(force=True)
                api_latency = (time.time() - start_time) * 1000
                
                if sync_success:
                    status = processor.get_sync_status()
                    
                    # 验证API响应质量
                    assert status['time_synced'], f"{exchange} 时间同步失败"
                    assert abs(status['time_offset_ms']) < 5000, f"{exchange} 时间偏移过大"
                    assert api_latency < 10000, f"{exchange} API延迟过高"
                    
                    test_cases.append({
                        "case": f"{exchange}_real_api_sync",
                        "status": "PASS",
                        "api_latency_ms": api_latency,
                        "time_offset_ms": status['time_offset_ms'],
                        "sync_success": True
                    })
                    
                    print(f"    ✅ {exchange.upper()}: API同步成功 (延迟{api_latency:.1f}ms, 偏移{status['time_offset_ms']}ms)")
                else:
                    test_cases.append({
                        "case": f"{exchange}_real_api_sync",
                        "status": "FAIL",
                        "api_latency_ms": api_latency,
                        "sync_success": False
                    })
                    print(f"    ❌ {exchange.upper()}: API同步失败")
                    raise AssertionError(f"{exchange} 真实API同步失败")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases
            }
            
            print(f"✅ 真实API响应处理测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 真实API响应处理测试失败: {e}")
            return False
    
    async def test_network_fluctuation_simulation(self):
        """测试网络波动模拟"""
        print("🔍 测试网络波动模拟...")
        
        test_name = "network_fluctuation_simulation"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 模拟网络延迟场景
            print("  📋 模拟网络延迟场景")
            
            exchanges = ["gate", "bybit", "okx"]
            delay_scenarios = [0.1, 0.5, 1.0, 2.0]  # 不同延迟场景
            
            for delay in delay_scenarios:
                print(f"    🌐 测试网络延迟: {delay}s")
                
                for exchange in exchanges:
                    processor = get_timestamp_processor(exchange)
                    
                    # 模拟网络延迟
                    await asyncio.sleep(delay)
                    
                    start_time = time.time()
                    timestamp = processor.get_synced_timestamp()
                    response_time = (time.time() - start_time) * 1000
                    
                    # 验证在网络延迟下的响应
                    assert isinstance(timestamp, int), f"{exchange} 延迟{delay}s下时间戳格式错误"
                    assert timestamp > 1700000000000, f"{exchange} 延迟{delay}s下时间戳无效"
                    
                    current_time = int(time.time() * 1000)
                    time_diff = abs(timestamp - current_time)
                    
                    test_cases.append({
                        "case": f"{exchange}_delay_{delay}s",
                        "status": "PASS",
                        "network_delay_s": delay,
                        "response_time_ms": response_time,
                        "time_diff_ms": time_diff,
                        "timestamp": timestamp
                    })
                    
                    print(f"      ✅ {exchange.upper()}: 响应正常 (响应{response_time:.1f}ms, 差异{time_diff:.1f}ms)")
            
            # 模拟并发网络请求
            print("  📋 模拟并发网络请求压力")
            
            concurrent_tasks = []
            for exchange in exchanges:
                processor = get_timestamp_processor(exchange)
                
                # 创建多个并发任务
                for i in range(5):
                    task = processor.get_synced_timestamp()
                    concurrent_tasks.append((exchange, i, task))
            
            # 验证并发请求结果
            for exchange, task_id, timestamp in concurrent_tasks:
                assert isinstance(timestamp, int), f"{exchange} 并发任务{task_id}时间戳格式错误"
                
                test_cases.append({
                    "case": f"{exchange}_concurrent_{task_id}",
                    "status": "PASS",
                    "timestamp": timestamp
                })
            
            print(f"    ✅ 并发网络请求测试通过 ({len(concurrent_tasks)} 个并发任务)")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases
            }
            
            print(f"✅ 网络波动模拟测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 网络波动模拟测试失败: {e}")
            return False
    
    async def test_extreme_scenarios(self):
        """测试极限场景"""
        print("🔍 测试极限场景...")
        
        test_name = "extreme_scenarios"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor, get_synced_timestamp
            
            # 测试极限时间差场景
            print("  📋 测试极限时间差场景")
            
            exchanges = ["gate", "bybit", "okx"]
            
            # 模拟极限时间差数据
            extreme_timestamp_data = [
                {"ts": int(time.time() * 1000) + 10000},  # 未来10秒
                {"timestamp": int(time.time() * 1000) - 10000},  # 过去10秒
                {"time": int(time.time() * 1000) + 5000},  # 未来5秒
                {"server_time": int(time.time() * 1000) - 5000}  # 过去5秒
            ]
            
            for i, extreme_data in enumerate(extreme_timestamp_data):
                print(f"    ⚡ 测试极限数据场景 {i+1}")
                
                for exchange in exchanges:
                    processor = get_timestamp_processor(exchange)
                    
                    # 测试极限数据处理
                    timestamp = processor.get_synced_timestamp(extreme_data)
                    
                    # 验证极限数据处理结果
                    assert isinstance(timestamp, int), f"{exchange} 极限场景{i+1}时间戳格式错误"
                    
                    current_time = int(time.time() * 1000)
                    time_diff = abs(timestamp - current_time)
                    
                    # 验证时间戳合理性（应该被修正到合理范围）
                    assert time_diff < 10000, f"{exchange} 极限场景{i+1}时间戳未被正确修正"
                    
                    test_cases.append({
                        "case": f"{exchange}_extreme_scenario_{i+1}",
                        "status": "PASS",
                        "extreme_data": extreme_data,
                        "corrected_timestamp": timestamp,
                        "time_diff_ms": time_diff
                    })
                    
                    print(f"      ✅ {exchange.upper()}: 极限数据处理正常 (修正后差异{time_diff:.1f}ms)")
            
            # 测试高频调用压力
            print("  📋 测试高频调用压力")
            
            high_freq_results = []
            for exchange in exchanges:
                print(f"    🚀 {exchange.upper()} 高频调用测试")
                
                start_time = time.time()
                
                # 高频调用100次
                for i in range(100):
                    timestamp = get_synced_timestamp(exchange)
                    high_freq_results.append((exchange, i, timestamp))
                
                duration = time.time() - start_time
                avg_latency = (duration / 100) * 1000
                
                # 验证高频调用性能
                assert duration < 10.0, f"{exchange} 高频调用耗时过长"
                assert avg_latency < 100, f"{exchange} 平均延迟过高"
                
                test_cases.append({
                    "case": f"{exchange}_high_frequency_calls",
                    "status": "PASS",
                    "total_calls": 100,
                    "total_duration_s": duration,
                    "avg_latency_ms": avg_latency
                })
                
                print(f"      ✅ {exchange.upper()}: 高频调用正常 (100次/{duration:.2f}s, 平均{avg_latency:.1f}ms)")
            
            # 测试内存使用稳定性
            print("  📋 测试内存使用稳定性")
            
            import psutil
            import gc
            
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 执行大量时间戳操作
            for _ in range(1000):
                for exchange in exchanges:
                    get_synced_timestamp(exchange)
            
            gc.collect()  # 强制垃圾回收
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # 验证内存使用稳定性
            assert memory_increase < 50, f"内存增长过多: {memory_increase:.1f}MB"
            
            test_cases.append({
                "case": "memory_stability",
                "status": "PASS",
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": memory_increase
            })
            
            print(f"    ✅ 内存使用稳定 (增长{memory_increase:.1f}MB)")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases
            }
            
            print(f"✅ 极限场景测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 极限场景测试失败: {e}")
            return False
    
    async def test_production_readiness(self):
        """测试生产就绪性"""
        print("🔍 测试生产就绪性...")
        
        test_name = "production_readiness"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import check_all_timestamp_sync_health
            from websocket.ws_manager import WebSocketManager
            
            # 测试系统健康检查
            print("  📋 测试系统健康检查")
            
            health_status = await check_all_timestamp_sync_health()
            
            all_healthy = True
            for exchange, status in health_status.items():
                is_healthy = status.get('is_healthy', False)
                health_level = status.get('health_level', 'UNKNOWN')
                
                if not is_healthy:
                    all_healthy = False
                
                test_cases.append({
                    "case": f"{exchange}_production_health",
                    "status": "PASS" if is_healthy else "FAIL",
                    "health_level": health_level,
                    "is_healthy": is_healthy
                })
                
                if is_healthy:
                    print(f"    ✅ {exchange.upper()}: 生产健康状态良好 ({health_level})")
                else:
                    print(f"    ❌ {exchange.upper()}: 生产健康状态异常 ({health_level})")
            
            if not all_healthy:
                raise AssertionError("系统健康检查未完全通过")
            
            # 测试完整启动流程
            print("  📋 测试完整启动流程")
            
            ws_manager = WebSocketManager()
            
            # 模拟完整启动流程
            start_time = time.time()
            await ws_manager._centralized_time_sync()
            startup_duration = time.time() - start_time
            
            # 验证启动性能
            assert startup_duration < 30.0, f"启动耗时过长: {startup_duration:.1f}s"
            
            test_cases.append({
                "case": "complete_startup_flow",
                "status": "PASS",
                "startup_duration_s": startup_duration
            })
            
            print(f"    ✅ 完整启动流程正常 (耗时{startup_duration:.1f}s)")
            
            # 测试错误恢复能力
            print("  📋 测试错误恢复能力")
            
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            for exchange in ["gate", "bybit", "okx"]:
                processor = get_timestamp_processor(exchange)
                
                # 模拟错误状态
                original_synced = processor.time_synced
                processor.time_synced = False
                
                # 测试错误恢复
                timestamp = processor.get_synced_timestamp()
                
                # 验证错误恢复
                assert isinstance(timestamp, int), f"{exchange} 错误恢复失败"
                
                # 恢复原始状态
                processor.time_synced = original_synced
                
                test_cases.append({
                    "case": f"{exchange}_error_recovery",
                    "status": "PASS",
                    "recovery_timestamp": timestamp
                })
                
                print(f"    ✅ {exchange.upper()}: 错误恢复能力正常")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases
            }
            
            print(f"✅ 生产就绪性测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 生产就绪性测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有生产测试"""
        print("🚀 启动机构级别测试 - 第三段：生产测试")
        print("=" * 80)
        
        tests = [
            self.test_real_api_responses,
            self.test_network_fluctuation_simulation,
            self.test_extreme_scenarios,
            self.test_production_readiness
        ]
        
        passed_tests = 0
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed_tests += 1
                print()  # 空行分隔
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
                print()
        
        # 更新总结
        self.test_results["summary"]["total_tests"] = len(tests)
        self.test_results["summary"]["passed_tests"] = passed_tests
        self.test_results["summary"]["failed_tests"] = len(tests) - passed_tests
        self.test_results["summary"]["success_rate"] = (passed_tests / len(tests)) * 100
        
        # 保存测试结果
        output_file = "diagnostic_scripts/institutional_test_stage3_result.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
            print(f"✅ 测试结果已保存到: {output_file}")
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")
        
        # 生成测试报告
        print("=" * 80)
        print("📊 生产测试报告")
        print("=" * 80)
        print(f"总测试数: {self.test_results['summary']['total_tests']}")
        print(f"通过测试: {self.test_results['summary']['passed_tests']}")
        print(f"失败测试: {self.test_results['summary']['failed_tests']}")
        print(f"成功率: {self.test_results['summary']['success_rate']:.1f}%")
        
        if self.test_results['summary']['success_rate'] == 100.0:
            print("🎉 生产测试100%通过 - 系统已完全准备好部署到实盘")
            return True
        else:
            print("❌ 生产测试未完全通过 - 系统尚未准备好部署到实盘")
            return False

async def main():
    """主函数"""
    tester = InstitutionalGradeTestStage3()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 第三段测试结论: 系统已完全准备好部署到实盘，零失误保证")
        return 0
    else:
        print("\n❌ 第三段测试失败: 系统尚未准备好实盘部署，必须先修复")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
