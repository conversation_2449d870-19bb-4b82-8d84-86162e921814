{"stage": "生产测试", "timestamp": 1754035462, "tests": {"real_api_responses": {"status": "PASS", "test_cases": [{"case": "gate_real_api_sync", "status": "PASS", "api_latency_ms": 248.54540824890137, "time_offset_ms": -5, "sync_success": true}, {"case": "bybit_real_api_sync", "status": "PASS", "api_latency_ms": 130.967378616333, "time_offset_ms": -38, "sync_success": true}, {"case": "okx_real_api_sync", "status": "PASS", "api_latency_ms": 130.22160530090332, "time_offset_ms": -35, "sync_success": true}]}, "network_fluctuation_simulation": {"status": "PASS", "test_cases": [{"case": "gate_delay_0.1s", "status": "PASS", "network_delay_s": 0.1, "response_time_ms": 0.1239776611328125, "time_diff_ms": 3, "timestamp": 1754035463160}, {"case": "bybit_delay_0.1s", "status": "PASS", "network_delay_s": 0.1, "response_time_ms": 0.1304149627685547, "time_diff_ms": 42, "timestamp": 1754035463222}, {"case": "okx_delay_0.1s", "status": "PASS", "network_delay_s": 0.1, "response_time_ms": 0.14591217041015625, "time_diff_ms": 39, "timestamp": 1754035463325}, {"case": "gate_delay_0.5s", "status": "PASS", "network_delay_s": 0.5, "response_time_ms": 0.12683868408203125, "time_diff_ms": 5, "timestamp": 1754035463860}, {"case": "bybit_delay_0.5s", "status": "PASS", "network_delay_s": 0.5, "response_time_ms": 0.11324882507324219, "time_diff_ms": 44, "timestamp": 1754035464322}, {"case": "okx_delay_0.5s", "status": "PASS", "network_delay_s": 0.5, "response_time_ms": 0.10633468627929688, "time_diff_ms": 42, "timestamp": 1754035464825}, {"case": "gate_delay_1.0s", "status": "PASS", "network_delay_s": 1.0, "response_time_ms": 0.08726119995117188, "time_diff_ms": 7, "timestamp": 1754035465860}, {"case": "bybit_delay_1.0s", "status": "PASS", "network_delay_s": 1.0, "response_time_ms": 0.2028942108154297, "time_diff_ms": 47, "timestamp": 1754035466822}, {"case": "okx_delay_1.0s", "status": "PASS", "network_delay_s": 1.0, "response_time_ms": 0.09918212890625, "time_diff_ms": 35, "timestamp": 1754035467835}, {"case": "gate_delay_2.0s", "status": "PASS", "network_delay_s": 2.0, "response_time_ms": 0.11324882507324219, "time_diff_ms": 3, "timestamp": 1754035469870}, {"case": "bybit_delay_2.0s", "status": "PASS", "network_delay_s": 2.0, "response_time_ms": 0.09107589721679688, "time_diff_ms": 42, "timestamp": 1754035471832}, {"case": "okx_delay_2.0s", "status": "PASS", "network_delay_s": 2.0, "response_time_ms": 0.13518333435058594, "time_diff_ms": 42, "timestamp": 1754035473835}, {"case": "gate_concurrent_0", "status": "PASS", "timestamp": 1754035473870}, {"case": "gate_concurrent_1", "status": "PASS", "timestamp": 1754035473870}, {"case": "gate_concurrent_2", "status": "PASS", "timestamp": 1754035473870}, {"case": "gate_concurrent_3", "status": "PASS", "timestamp": 1754035473870}, {"case": "gate_concurrent_4", "status": "PASS", "timestamp": 1754035473870}, {"case": "bybit_concurrent_0", "status": "PASS", "timestamp": 1754035473832}, {"case": "bybit_concurrent_1", "status": "PASS", "timestamp": 1754035473832}, {"case": "bybit_concurrent_2", "status": "PASS", "timestamp": 1754035473832}, {"case": "bybit_concurrent_3", "status": "PASS", "timestamp": 1754035473832}, {"case": "bybit_concurrent_4", "status": "PASS", "timestamp": 1754035473832}, {"case": "okx_concurrent_0", "status": "PASS", "timestamp": 1754035473835}, {"case": "okx_concurrent_1", "status": "PASS", "timestamp": 1754035473835}, {"case": "okx_concurrent_2", "status": "PASS", "timestamp": 1754035473835}, {"case": "okx_concurrent_3", "status": "PASS", "timestamp": 1754035473835}, {"case": "okx_concurrent_4", "status": "PASS", "timestamp": 1754035473835}]}, "extreme_scenarios": {"status": "PASS", "test_cases": [{"case": "gate_extreme_scenario_1", "status": "PASS", "extreme_data": {"ts": 1754035483877}, "corrected_timestamp": 1754035473870, "time_diff_ms": 7}, {"case": "bybit_extreme_scenario_1", "status": "PASS", "extreme_data": {"ts": 1754035483877}, "corrected_timestamp": 1754035473832, "time_diff_ms": 47}, {"case": "okx_extreme_scenario_1", "status": "PASS", "extreme_data": {"ts": 1754035483877}, "corrected_timestamp": 1754035473835, "time_diff_ms": 44}, {"case": "gate_extreme_scenario_2", "status": "PASS", "extreme_data": {"timestamp": 1754035463877}, "corrected_timestamp": 1754035473870, "time_diff_ms": 9}, {"case": "bybit_extreme_scenario_2", "status": "PASS", "extreme_data": {"timestamp": 1754035463877}, "corrected_timestamp": 1754035473832, "time_diff_ms": 48}, {"case": "okx_extreme_scenario_2", "status": "PASS", "extreme_data": {"timestamp": 1754035463877}, "corrected_timestamp": 1754035473845, "time_diff_ms": 35}, {"case": "gate_extreme_scenario_3", "status": "PASS", "extreme_data": {"time": 1754035478877}, "corrected_timestamp": 1754035473880, "time_diff_ms": 0}, {"case": "bybit_extreme_scenario_3", "status": "PASS", "extreme_data": {"time": 1754035478877}, "corrected_timestamp": 1754035473842, "time_diff_ms": 38}, {"case": "okx_extreme_scenario_3", "status": "PASS", "extreme_data": {"time": 1754035478877}, "corrected_timestamp": 1754035473845, "time_diff_ms": 35}, {"case": "gate_extreme_scenario_4", "status": "PASS", "extreme_data": {"server_time": 1754035468877}, "corrected_timestamp": 1754035473880, "time_diff_ms": 1}, {"case": "bybit_extreme_scenario_4", "status": "PASS", "extreme_data": {"server_time": 1754035468877}, "corrected_timestamp": 1754035473842, "time_diff_ms": 39}, {"case": "okx_extreme_scenario_4", "status": "PASS", "extreme_data": {"server_time": 1754035468877}, "corrected_timestamp": 1754035473845, "time_diff_ms": 36}, {"case": "gate_high_frequency_calls", "status": "PASS", "total_calls": 100, "total_duration_s": 0.0014460086822509766, "avg_latency_ms": 0.014460086822509766}, {"case": "bybit_high_frequency_calls", "status": "PASS", "total_calls": 100, "total_duration_s": 0.0015838146209716797, "avg_latency_ms": 0.015838146209716797}, {"case": "okx_high_frequency_calls", "status": "PASS", "total_calls": 100, "total_duration_s": 0.0013797283172607422, "avg_latency_ms": 0.013797283172607422}, {"case": "memory_stability", "status": "PASS", "initial_memory_mb": 38.546875, "final_memory_mb": 38.546875, "memory_increase_mb": 0.0}]}, "production_readiness": {"status": "PASS", "test_cases": [{"case": "gate_production_health", "status": "PASS", "health_level": "GOOD", "is_healthy": true}, {"case": "bybit_production_health", "status": "PASS", "health_level": "GOOD", "is_healthy": true}, {"case": "okx_production_health", "status": "PASS", "health_level": "GOOD", "is_healthy": true}, {"case": "complete_startup_flow", "status": "PASS", "startup_duration_s": 2.333096981048584}, {"case": "gate_error_recovery", "status": "PASS", "recovery_timestamp": 1754035476300}, {"case": "bybit_error_recovery", "status": "PASS", "recovery_timestamp": 1754035476300}, {"case": "okx_error_recovery", "status": "PASS", "recovery_timestamp": 1754035476300}]}}, "summary": {"total_tests": 4, "passed_tests": 4, "failed_tests": 0, "success_rate": 100.0}}