{"stage": "基础核心测试", "timestamp": 1754035264, "tests": {"unified_timestamp_processor_core": {"status": "PASS", "test_cases": [{"case": "gate_basic_timestamp", "status": "PASS", "timestamp": 1754035264530, "time_diff_ms": 8}, {"case": "bybit_basic_timestamp", "status": "PASS", "timestamp": 1754035264530, "time_diff_ms": 8}, {"case": "okx_basic_timestamp", "status": "PASS", "timestamp": 1754035264530, "time_diff_ms": 8}, {"case": "gate_boundary_none", "status": "PASS", "timestamp_none": 1754035264530, "timestamp_empty": 1754035264530}, {"case": "bybit_boundary_none", "status": "PASS", "timestamp_none": 1754035264530, "timestamp_empty": 1754035264530}, {"case": "okx_boundary_none", "status": "PASS", "timestamp_none": 1754035264530, "timestamp_empty": 1754035264530}, {"case": "gate_error_handling_0", "status": "PASS", "invalid_data": {"invalid": "data"}, "fallback_timestamp": 1754035264530}, {"case": "gate_error_handling_1", "status": "PASS", "invalid_data": {"timestamp": "invalid"}, "fallback_timestamp": 1754035264530}, {"case": "gate_error_handling_2", "status": "PASS", "invalid_data": {"ts": -1}, "fallback_timestamp": 1754035264530}, {"case": "gate_error_handling_3", "status": "PASS", "invalid_data": {"time": "not_a_number"}, "fallback_timestamp": 1754035264530}, {"case": "bybit_error_handling_0", "status": "PASS", "invalid_data": {"invalid": "data"}, "fallback_timestamp": 1754035264540}, {"case": "bybit_error_handling_1", "status": "PASS", "invalid_data": {"timestamp": "invalid"}, "fallback_timestamp": 1754035264540}, {"case": "bybit_error_handling_2", "status": "PASS", "invalid_data": {"ts": -1}, "fallback_timestamp": 1754035264540}, {"case": "bybit_error_handling_3", "status": "PASS", "invalid_data": {"time": "not_a_number"}, "fallback_timestamp": 1754035264540}, {"case": "okx_error_handling_0", "status": "PASS", "invalid_data": {"invalid": "data"}, "fallback_timestamp": 1754035264540}, {"case": "okx_error_handling_1", "status": "PASS", "invalid_data": {"timestamp": "invalid"}, "fallback_timestamp": 1754035264540}, {"case": "okx_error_handling_2", "status": "PASS", "invalid_data": {"ts": -1}, "fallback_timestamp": 1754035264540}, {"case": "okx_error_handling_3", "status": "PASS", "invalid_data": {"time": "not_a_number"}, "fallback_timestamp": 1754035264540}, {"case": "cross_sync_gate_bybit", "status": "PASS", "is_synced": true, "time_diff_ms": 0, "threshold_ms": 800}, {"case": "cross_sync_gate_okx", "status": "PASS", "is_synced": true, "time_diff_ms": 0, "threshold_ms": 800}, {"case": "cross_sync_bybit_okx", "status": "PASS", "is_synced": true, "time_diff_ms": 0, "threshold_ms": 800}], "total_cases": 21, "passed_cases": 21}, "websocket_client_integration": {"status": "PASS", "test_cases": [{"case": "gate_client_integration", "status": "PASS", "client_class": "GateWebSocketClient", "sync_status": {"exchange": "gate", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}, {"case": "bybit_client_integration", "status": "PASS", "client_class": "BybitWebSocketClient", "sync_status": {"exchange": "bybit", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}, {"case": "okx_client_integration", "status": "PASS", "client_class": "OKXWebSocketClient", "sync_status": {"exchange": "okx", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}], "total_cases": 3, "passed_cases": 3}, "centralized_sync_mechanism": {"status": "PASS", "test_cases": [{"case": "gate_centralized_sync", "status": "PASS", "sync_success": true}, {"case": "bybit_centralized_sync", "status": "PASS", "sync_success": true}, {"case": "okx_centralized_sync", "status": "PASS", "sync_success": true}, {"case": "ws_manager_centralized_sync", "status": "PASS", "method_exists": true}], "total_cases": 4, "passed_cases": 4}}, "summary": {"total_tests": 3, "passed_tests": 3, "failed_tests": 0, "success_rate": 100.0}}