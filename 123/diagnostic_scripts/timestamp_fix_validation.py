#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳修复验证脚本
验证OpportunityScanner中时间戳单位不一致问题的修复效果
"""

import sys
import os
import time
import logging
from decimal import Decimal

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_timestamp_unit_consistency():
    """测试时间戳单位一致性"""
    logger.info("🔥 开始验证时间戳单位一致性修复...")
    
    try:
        # 导入统一时间戳处理函数
        from websocket.unified_timestamp_processor import calculate_data_age, ensure_milliseconds_timestamp
        
        # 模拟测试数据
        current_time = time.time()  # 秒级时间戳
        current_time_ms = int(current_time * 1000)  # 毫秒级时间戳
        
        # 测试1: 验证毫秒级时间戳处理
        test_timestamp_ms = current_time_ms - 100  # 100ms前的时间戳
        age_seconds = calculate_data_age(test_timestamp_ms, current_time)
        expected_age = 0.1  # 应该约为0.1秒
        
        logger.info(f"✅ 测试1 - 毫秒级时间戳处理:")
        logger.info(f"   输入时间戳: {test_timestamp_ms} (毫秒级)")
        logger.info(f"   当前时间: {current_time} (秒级)")
        logger.info(f"   计算年龄: {age_seconds:.3f} 秒")
        logger.info(f"   预期年龄: {expected_age:.3f} 秒")
        
        if abs(age_seconds - expected_age) < 0.05:  # 50ms容忍度
            logger.info("   ✅ 测试通过：时间戳单位处理正确")
        else:
            logger.error("   ❌ 测试失败：时间戳单位处理错误")
            return False
        
        # 测试2: 验证秒级时间戳处理
        test_timestamp_s = current_time - 0.1  # 0.1秒前的时间戳
        age_seconds_2 = calculate_data_age(test_timestamp_s, current_time)
        
        logger.info(f"✅ 测试2 - 秒级时间戳处理:")
        logger.info(f"   输入时间戳: {test_timestamp_s} (秒级)")
        logger.info(f"   计算年龄: {age_seconds_2:.3f} 秒")
        
        if abs(age_seconds_2 - expected_age) < 0.05:
            logger.info("   ✅ 测试通过：秒级时间戳转换正确")
        else:
            logger.error("   ❌ 测试失败：秒级时间戳转换错误")
            return False
            
        # 测试3: 验证跨交易所时间戳同步（模拟真实场景）
        logger.info(f"✅ 测试3 - 跨交易所时间戳同步验证:")
        
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        # 模拟Gate和OKX的时间戳（毫秒级）
        gate_timestamp = current_time_ms - 50  # Gate时间戳：50ms前
        okx_timestamp = current_time_ms - 150   # OKX时间戳：150ms前（模拟网络延迟）
        
        # 使用统一时间戳处理器验证同步
        processor = get_timestamp_processor("gate")
        is_synced, time_diff_ms = processor.validate_cross_exchange_sync(
            gate_timestamp, okx_timestamp, "gate", "okx", max_diff_ms=800
        )
        
        logger.info(f"   Gate时间戳: {gate_timestamp}")
        logger.info(f"   OKX时间戳: {okx_timestamp}")
        logger.info(f"   时间差: {time_diff_ms:.1f}ms")
        logger.info(f"   同步状态: {'✅ 同步' if is_synced else '❌ 不同步'}")
        
        if is_synced and time_diff_ms == 100.0:  # 预期时间差100ms
            logger.info("   ✅ 测试通过：跨交易所时间戳同步验证正确")
        else:
            logger.error(f"   ❌ 测试失败：预期时间差100ms，实际{time_diff_ms}ms")
            return False
        
        # 测试4: 验证OpportunityScanner中的修复
        logger.info(f"✅ 测试4 - OpportunityScanner时间戳处理验证:")
        
        # 模拟OpportunityScanner中修复后的代码逻辑
        spot_timestamp = current_time_ms - 200  # 200ms前
        futures_timestamp = current_time_ms - 300  # 300ms前
        
        # 使用修复后的逻辑
        data_age_spot_seconds = calculate_data_age(spot_timestamp, current_time)
        data_age_futures_seconds = calculate_data_age(futures_timestamp, current_time)
        data_age_spot = data_age_spot_seconds * 1000  # 转换为毫秒
        data_age_futures = data_age_futures_seconds * 1000  # 转换为毫秒
        
        logger.info(f"   现货时间戳: {spot_timestamp} -> 年龄: {data_age_spot:.1f}ms")
        logger.info(f"   期货时间戳: {futures_timestamp} -> 年龄: {data_age_futures:.1f}ms")
        
        # 验证计算结果是否正确
        if abs(data_age_spot - 200) < 50 and abs(data_age_futures - 300) < 50:
            logger.info("   ✅ 测试通过：OpportunityScanner时间戳处理修复成功")
        else:
            logger.error("   ❌ 测试失败：OpportunityScanner时间戳处理仍有问题")
            return False
        
        logger.info("🎉 所有时间戳一致性测试通过！修复验证成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 时间戳一致性测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_data_logging():
    """测试WebSocket数据日志记录"""
    logger.info("🔥 开始验证WebSocket数据日志记录...")
    
    try:
        # 检查日志文件是否存在且有数据
        logs_dir = os.path.join(project_root, "logs")
        
        # 检查关键日志文件
        log_files = [
            "websocket_performance_20250801.log",
            "websocket_prices.log"
        ]
        
        for log_file in log_files:
            log_path = os.path.join(logs_dir, log_file)
            if os.path.exists(log_path):
                file_size = os.path.getsize(log_path)
                logger.info(f"✅ {log_file}: 存在，大小 {file_size} 字节")
                
                # 简单统计内容
                if file_size > 0:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        logger.info(f"   包含 {len(lines)} 行数据")
                        
                        # 统计关键信息
                        if "performance" in log_file:
                            sync_errors = sum(1 for line in lines if "跨交易所时间戳不同步" in line)
                            logger.info(f"   时间戳不同步记录: {sync_errors} 条")
                        elif "prices" in log_file:
                            price_records = sum(1 for line in lines if "🚀" in line or "💰" in line)
                            logger.info(f"   价格记录: {price_records} 条")
            else:
                logger.warning(f"⚠️ {log_file}: 不存在")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ WebSocket数据日志验证异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🔥 时间戳修复验证脚本")
    logger.info("=" * 80)
    
    # 执行测试
    tests_passed = 0
    total_tests = 2
    
    if test_timestamp_unit_consistency():
        tests_passed += 1
        
    if test_websocket_data_logging():
        tests_passed += 1
    
    # 输出结果
    logger.info("=" * 80)
    logger.info(f"📊 验证结果: {tests_passed}/{total_tests} 测试通过")
    
    if tests_passed == total_tests:
        logger.info("🎉 所有验证测试通过！时间戳修复成功！")
        logger.info("💡 建议：")
        logger.info("   1. 重新启动系统验证实际效果")
        logger.info("   2. 观察日志中时间戳不同步错误是否减少")
        logger.info("   3. 检查套利机会是否不再被错误丢弃")
        return True
    else:
        logger.error("❌ 验证测试失败，需要进一步检查修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)