{"stage": "复杂系统级联测试", "timestamp": **********, "tests": {"multi_exchange_coordination": {"status": "PASS", "test_cases": [{"case": "gate_health_check", "status": "FAIL", "health_level": "WARNING", "health_status": {"exchange": "gate", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": false, "health_level": "WARNING"}}, {"case": "bybit_health_check", "status": "FAIL", "health_level": "WARNING", "health_status": {"exchange": "bybit", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": false, "health_level": "WARNING"}}, {"case": "okx_health_check", "status": "FAIL", "health_level": "WARNING", "health_status": {"exchange": "okx", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": false, "health_level": "WARNING"}}, {"case": "cross_sync_gate_bybit", "status": "PASS", "time_diff_ms": 0, "threshold_ms": 800}, {"case": "cross_sync_gate_okx", "status": "PASS", "time_diff_ms": 0, "threshold_ms": 800}, {"case": "cross_sync_bybit_okx", "status": "PASS", "time_diff_ms": 0, "threshold_ms": 800}, {"case": "opportunity_scanner_timestamp_validation", "status": "PASS", "spot_timestamp": **********850, "futures_timestamp": **********850, "time_diff_ms": 0, "is_synced": true}], "sync_matrix": {"gate_bybit": {"is_synced": true, "time_diff_ms": 0}, "gate_okx": {"is_synced": true, "time_diff_ms": 0}, "bybit_okx": {"is_synced": true, "time_diff_ms": 0}}, "health_status": {"gate": {"exchange": "gate", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": false, "health_level": "WARNING"}, "bybit": {"exchange": "bybit", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": false, "health_level": "WARNING"}, "okx": {"exchange": "okx", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000, "is_healthy": false, "health_level": "WARNING"}}}, "websocket_manager_lifecycle": {"status": "PASS", "test_cases": [{"case": "initial_state", "status": "PASS", "running": false, "initialized": false}, {"case": "centralized_sync_integration", "status": "PASS", "method_available": true}, {"case": "state_management", "status": "PASS", "has_clients": true, "has_tasks": true}]}, "data_flow_consistency": {"status": "PASS", "test_cases": [{"case": "gate_unified_timestamp_interface", "status": "PASS", "timestamps": [1754035366230, 1754035366230, 1754035366230]}, {"case": "bybit_unified_timestamp_interface", "status": "PASS", "timestamps": [1754035366190, 1754035366190, 1754035366190]}, {"case": "okx_unified_timestamp_interface", "status": "PASS", "timestamps": [1754035366160, 1754035366160, 1754035366160]}, {"case": "gate_orderbook_formatting", "status": "PASS", "formatted_data_keys": ["data_type", "symbol", "exchange", "market_type", "asks", "bids", "timestamp", "asks_depth", "bids_depth", "price", "best_bid", "best_ask", "spread", "spread_percent", "spread_bps", "incomplete_orderbook", "low_liquidity", "data_quality_score"]}, {"case": "bybit_orderbook_formatting", "status": "PASS", "formatted_data_keys": ["data_type", "symbol", "exchange", "market_type", "asks", "bids", "timestamp", "asks_depth", "bids_depth", "price", "best_bid", "best_ask", "spread", "spread_percent", "spread_bps", "incomplete_orderbook", "low_liquidity", "data_quality_score"]}, {"case": "okx_orderbook_formatting", "status": "PASS", "formatted_data_keys": ["data_type", "symbol", "exchange", "market_type", "asks", "bids", "timestamp", "asks_depth", "bids_depth", "price", "best_bid", "best_ask", "spread", "spread_percent", "spread_bps", "incomplete_orderbook", "low_liquidity", "data_quality_score"]}]}}, "summary": {"total_tests": 3, "passed_tests": 3, "failed_tests": 0, "success_rate": 100.0}}