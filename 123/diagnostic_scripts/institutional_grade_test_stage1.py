#!/usr/bin/env python3
"""
机构级别测试 - 第一段：基础核心测试
模块单元功能验证：参数输入输出、边界检查、错误处理
确保修复点本身100%稳定
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any, List, Optional
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstitutionalGradeTestStage1:
    """机构级别测试 - 第一段：基础核心测试"""
    
    def __init__(self):
        self.test_results = {
            "stage": "基础核心测试",
            "timestamp": int(time.time()),
            "tests": {},
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "success_rate": 0.0
            }
        }
    
    async def test_unified_timestamp_processor_core(self):
        """测试统一时间戳处理器核心功能"""
        print("🔍 测试统一时间戳处理器核心功能...")
        
        test_name = "unified_timestamp_processor_core"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor, get_synced_timestamp
            
            # 测试用例1：基本时间戳获取
            print("  📋 测试用例1：基本时间戳获取")
            for exchange in ["gate", "bybit", "okx"]:
                processor = get_timestamp_processor(exchange)
                timestamp = processor.get_synced_timestamp()
                
                # 验证时间戳格式
                assert isinstance(timestamp, int), f"{exchange} 时间戳必须是整数"
                assert timestamp > 1700000000000, f"{exchange} 时间戳必须是毫秒级"
                assert timestamp < 2000000000000, f"{exchange} 时间戳不能超过2033年"
                
                current_time = int(time.time() * 1000)
                time_diff = abs(timestamp - current_time)
                assert time_diff < 10000, f"{exchange} 时间戳与当前时间差异不能超过10秒"
                
                test_cases.append({
                    "case": f"{exchange}_basic_timestamp",
                    "status": "PASS",
                    "timestamp": timestamp,
                    "time_diff_ms": time_diff
                })
                print(f"    ✅ {exchange.upper()}: 时间戳={timestamp}, 差异={time_diff}ms")
            
            # 测试用例2：边界条件测试
            print("  📋 测试用例2：边界条件测试")
            
            # 测试None数据
            for exchange in ["gate", "bybit", "okx"]:
                processor = get_timestamp_processor(exchange)
                timestamp_none = processor.get_synced_timestamp(None)
                timestamp_empty = processor.get_synced_timestamp({})
                
                assert isinstance(timestamp_none, int), f"{exchange} None数据应返回整数时间戳"
                assert isinstance(timestamp_empty, int), f"{exchange} 空数据应返回整数时间戳"
                
                test_cases.append({
                    "case": f"{exchange}_boundary_none",
                    "status": "PASS",
                    "timestamp_none": timestamp_none,
                    "timestamp_empty": timestamp_empty
                })
                print(f"    ✅ {exchange.upper()}: None处理正常")
            
            # 测试用例3：错误处理
            print("  📋 测试用例3：错误处理")
            
            # 测试无效数据
            invalid_data_cases = [
                {"invalid": "data"},
                {"timestamp": "invalid"},
                {"ts": -1},
                {"time": "not_a_number"}
            ]
            
            for exchange in ["gate", "bybit", "okx"]:
                processor = get_timestamp_processor(exchange)
                
                for i, invalid_data in enumerate(invalid_data_cases):
                    try:
                        timestamp = processor.get_synced_timestamp(invalid_data)
                        assert isinstance(timestamp, int), f"{exchange} 无效数据应返回有效时间戳"
                        
                        test_cases.append({
                            "case": f"{exchange}_error_handling_{i}",
                            "status": "PASS",
                            "invalid_data": invalid_data,
                            "fallback_timestamp": timestamp
                        })
                        
                    except Exception as e:
                        test_cases.append({
                            "case": f"{exchange}_error_handling_{i}",
                            "status": "FAIL",
                            "error": str(e),
                            "invalid_data": invalid_data
                        })
                        print(f"    ❌ {exchange.upper()}: 错误处理失败 - {e}")
                        raise
                
                print(f"    ✅ {exchange.upper()}: 错误处理正常")
            
            # 测试用例4：跨交易所同步验证
            print("  📋 测试用例4：跨交易所同步验证")
            
            exchanges = ["gate", "bybit", "okx"]
            timestamps = {}
            
            for exchange in exchanges:
                processor = get_timestamp_processor(exchange)
                timestamps[exchange] = processor.get_synced_timestamp()
            
            # 验证跨交易所时间差
            for i, exchange1 in enumerate(exchanges):
                for exchange2 in exchanges[i+1:]:
                    processor1 = get_timestamp_processor(exchange1)
                    
                    is_synced, time_diff = processor1.validate_cross_exchange_sync(
                        timestamps[exchange1], timestamps[exchange2], 
                        exchange1, exchange2, max_diff_ms=800
                    )
                    
                    test_cases.append({
                        "case": f"cross_sync_{exchange1}_{exchange2}",
                        "status": "PASS" if is_synced else "FAIL",
                        "is_synced": is_synced,
                        "time_diff_ms": time_diff,
                        "threshold_ms": 800
                    })
                    
                    if is_synced:
                        print(f"    ✅ {exchange1.upper()} vs {exchange2.upper()}: 同步正常, 差异={time_diff:.1f}ms")
                    else:
                        print(f"    ❌ {exchange1.upper()} vs {exchange2.upper()}: 同步失败, 差异={time_diff:.1f}ms")
                        raise AssertionError(f"跨交易所同步失败: {exchange1} vs {exchange2}")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases,
                "total_cases": len(test_cases),
                "passed_cases": len([c for c in test_cases if c["status"] == "PASS"])
            }
            
            print(f"✅ 统一时间戳处理器核心功能测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 统一时间戳处理器核心功能测试失败: {e}")
            return False
    
    async def test_websocket_client_integration(self):
        """测试WebSocket客户端集成"""
        print("🔍 测试WebSocket客户端集成...")
        
        test_name = "websocket_client_integration"
        test_cases = []
        
        try:
            # 测试各个WebSocket客户端的时间戳处理
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient
            
            clients = [
                ("gate", GateWebSocketClient, "spot"),
                ("bybit", BybitWebSocketClient, "spot"),
                ("okx", OKXWebSocketClient, "spot")
            ]
            
            for exchange_name, client_class, market_type in clients:
                print(f"  📋 测试 {exchange_name.upper()} WebSocket客户端")
                
                # 创建客户端实例
                client = client_class(market_type)
                
                # 验证客户端属性
                assert hasattr(client, 'exchange_name'), f"{exchange_name} 客户端缺少 exchange_name 属性"
                assert hasattr(client, 'market_type'), f"{exchange_name} 客户端缺少 market_type 属性"
                
                # 验证时间戳处理器集成
                from websocket.unified_timestamp_processor import get_timestamp_processor
                processor = get_timestamp_processor(exchange_name)
                
                # 验证处理器状态
                status = processor.get_sync_status()
                assert isinstance(status, dict), f"{exchange_name} 同步状态必须是字典"
                assert 'time_synced' in status, f"{exchange_name} 状态缺少 time_synced"
                assert 'time_offset_ms' in status, f"{exchange_name} 状态缺少 time_offset_ms"
                
                test_cases.append({
                    "case": f"{exchange_name}_client_integration",
                    "status": "PASS",
                    "client_class": client_class.__name__,
                    "sync_status": status
                })
                
                print(f"    ✅ {exchange_name.upper()}: 客户端集成正常")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases,
                "total_cases": len(test_cases),
                "passed_cases": len(test_cases)
            }
            
            print(f"✅ WebSocket客户端集成测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ WebSocket客户端集成测试失败: {e}")
            return False
    
    async def test_centralized_sync_mechanism(self):
        """测试集中式同步机制"""
        print("🔍 测试集中式同步机制...")
        
        test_name = "centralized_sync_mechanism"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            from websocket.ws_manager import WebSocketManager
            
            # 测试集中式初始化
            print("  📋 测试集中式时间戳处理器初始化")
            
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            
            # 验证同步结果
            assert isinstance(sync_results, dict), "同步结果必须是字典"
            assert len(sync_results) == 3, "必须包含3个交易所的同步结果"
            
            for exchange in ["gate", "bybit", "okx"]:
                assert exchange in sync_results, f"缺少 {exchange} 的同步结果"
                assert isinstance(sync_results[exchange], bool), f"{exchange} 同步结果必须是布尔值"
                
                test_cases.append({
                    "case": f"{exchange}_centralized_sync",
                    "status": "PASS" if sync_results[exchange] else "FAIL",
                    "sync_success": sync_results[exchange]
                })
                
                if sync_results[exchange]:
                    print(f"    ✅ {exchange.upper()}: 集中式同步成功")
                else:
                    print(f"    ❌ {exchange.upper()}: 集中式同步失败")
            
            # 测试WebSocket管理器集成
            print("  📋 测试WebSocket管理器集中式同步")
            
            ws_manager = WebSocketManager()
            
            # 验证集中式同步方法存在
            assert hasattr(ws_manager, '_centralized_time_sync'), "WebSocket管理器缺少集中式同步方法"
            
            # 执行集中式同步
            await ws_manager._centralized_time_sync()
            
            test_cases.append({
                "case": "ws_manager_centralized_sync",
                "status": "PASS",
                "method_exists": True
            })
            
            print("    ✅ WebSocket管理器集中式同步正常")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases,
                "total_cases": len(test_cases),
                "passed_cases": len([c for c in test_cases if c["status"] == "PASS"])
            }
            
            success_count = len([c for c in test_cases if c["status"] == "PASS"])
            print(f"✅ 集中式同步机制测试通过 ({success_count}/{len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 集中式同步机制测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有基础核心测试"""
        print("🚀 启动机构级别测试 - 第一段：基础核心测试")
        print("=" * 80)
        
        tests = [
            self.test_unified_timestamp_processor_core,
            self.test_websocket_client_integration,
            self.test_centralized_sync_mechanism
        ]
        
        passed_tests = 0
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed_tests += 1
                print()  # 空行分隔
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
                print()
        
        # 更新总结
        self.test_results["summary"]["total_tests"] = len(tests)
        self.test_results["summary"]["passed_tests"] = passed_tests
        self.test_results["summary"]["failed_tests"] = len(tests) - passed_tests
        self.test_results["summary"]["success_rate"] = (passed_tests / len(tests)) * 100
        
        # 保存测试结果
        output_file = "diagnostic_scripts/institutional_test_stage1_result.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
            print(f"✅ 测试结果已保存到: {output_file}")
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")
        
        # 生成测试报告
        print("=" * 80)
        print("📊 基础核心测试报告")
        print("=" * 80)
        print(f"总测试数: {self.test_results['summary']['total_tests']}")
        print(f"通过测试: {self.test_results['summary']['passed_tests']}")
        print(f"失败测试: {self.test_results['summary']['failed_tests']}")
        print(f"成功率: {self.test_results['summary']['success_rate']:.1f}%")
        
        if self.test_results['summary']['success_rate'] == 100.0:
            print("🎉 基础核心测试100%通过 - 修复点本身完全稳定")
            return True
        else:
            print("❌ 基础核心测试未完全通过 - 需要进一步修复")
            return False

async def main():
    """主函数"""
    tester = InstitutionalGradeTestStage1()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 第一段测试结论: 基础核心功能100%稳定，可以进行第二段测试")
        return 0
    else:
        print("\n❌ 第一段测试失败: 基础核心功能存在问题，必须先修复")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
