#!/usr/bin/env python3
"""
🔥 时间戳修复验证脚本 - 2025-08-01
验证时间戳浮点数问题和单位不一致问题的修复效果
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any, List, Tuple

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_timestamp_integer_consistency():
    """测试时间戳整数一致性"""
    print("🧪 测试时间戳整数一致性...")
    
    try:
        from websocket.unified_timestamp_processor import (
            ensure_milliseconds_timestamp,
            get_timestamp_processor
        )
        
        # 测试各种时间戳格式
        test_cases = [
            (1754055467805.0, "浮点毫秒"),
            (1754055467.805, "浮点秒"),
            (1754055467805, "整数毫秒"),
            (1754055467, "整数秒"),
        ]
        
        all_passed = True
        for timestamp, desc in test_cases:
            result = ensure_milliseconds_timestamp(timestamp)
            is_int = isinstance(result, int)
            print(f"  ✅ {desc}: {timestamp} -> {result} (整数: {is_int})")
            if not is_int:
                all_passed = False
                print(f"    ❌ 错误：返回类型不是整数，而是 {type(result)}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_cross_exchange_sync():
    """测试跨交易所同步检查"""
    print("\n🧪 测试跨交易所同步检查...")
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        processor = get_timestamp_processor("gate")
        current_ms = int(time.time() * 1000)
        
        test_cases = [
            (current_ms, current_ms + 14, "正常同步", True),
            (current_ms, current_ms + 500, "轻微不同步", True),
            (current_ms, current_ms + 900, "边界不同步", False),
            (current_ms, current_ms + 9940, "严重不同步", False),
        ]
        
        all_passed = True
        for ts1, ts2, desc, expected_sync in test_cases:
            is_synced, time_diff = processor.validate_cross_exchange_sync(
                ts1, ts2, "gate", "okx", max_diff_ms=800
            )
            
            # 检查时间差是否为整数或浮点数（但不应该是巨大的负数）
            is_reasonable_diff = 0 <= time_diff <= 20000  # 20秒内算合理
            
            passed = (is_synced == expected_sync) and is_reasonable_diff
            status = "✅" if passed else "❌"
            
            print(f"  {status} {desc}: 同步={is_synced}, 时间差={time_diff:.1f}ms")
            
            if not passed:
                all_passed = False
                if not is_reasonable_diff:
                    print(f"    ❌ 时间差异常: {time_diff}ms")
                if is_synced != expected_sync:
                    print(f"    ❌ 同步状态错误: 期望{expected_sync}, 实际{is_synced}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_age_calculation():
    """测试数据年龄计算"""
    print("\n🧪 测试数据年龄计算...")
    
    try:
        from websocket.unified_timestamp_processor import calculate_data_age
        
        current_time = time.time()
        current_ms = int(current_time * 1000)
        
        test_cases = [
            (current_ms, current_time, "当前时间", 0.0),
            (current_ms - 1000, current_time, "1秒前", 1.0),
            (current_ms - 500, current_time, "500ms前", 0.5),
            (current_ms + 1000, current_time, "1秒后（未来）", 1.0),
        ]
        
        all_passed = True
        for timestamp, current, desc, expected_age in test_cases:
            age = calculate_data_age(timestamp, current)
            
            # 允许小的误差（±0.1秒）
            is_correct = abs(age - expected_age) <= 0.1
            status = "✅" if is_correct else "❌"
            
            print(f"  {status} {desc}: 年龄={age:.3f}秒 (期望{expected_age:.1f}秒)")
            
            if not is_correct:
                all_passed = False
                print(f"    ❌ 年龄计算错误: 期望{expected_age:.1f}s, 实际{age:.3f}s")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_opportunity_scanner_fix():
    """测试OpportunityScanner修复"""
    print("\n🧪 测试OpportunityScanner时间戳修复...")
    
    try:
        from websocket.unified_timestamp_processor import calculate_data_age
        
        # 模拟OpportunityScanner中的场景
        current_time = int(time.time() * 1000)  # 毫秒级
        spot_timestamp = current_time - 200     # 200ms前
        futures_timestamp = current_time - 300  # 300ms前
        
        print(f"📊 测试数据:")
        print(f"  current_time: {current_time} (毫秒)")
        print(f"  spot_timestamp: {spot_timestamp} (毫秒)")
        print(f"  futures_timestamp: {futures_timestamp} (毫秒)")
        
        # 测试修复后的计算方式
        current_time_seconds = current_time / 1000  # 转换为秒
        data_age_spot_seconds = calculate_data_age(spot_timestamp, current_time_seconds)
        data_age_futures_seconds = calculate_data_age(futures_timestamp, current_time_seconds)
        data_age_spot = data_age_spot_seconds * 1000  # 转换为毫秒
        data_age_futures = data_age_futures_seconds * 1000
        
        print(f"\n✅ 修复后计算结果:")
        print(f"  data_age_spot: {data_age_spot:.1f}ms")
        print(f"  data_age_futures: {data_age_futures:.1f}ms")
        
        # 验证结果合理性
        spot_reasonable = 150 <= data_age_spot <= 250  # 200ms ± 50ms
        futures_reasonable = 250 <= data_age_futures <= 350  # 300ms ± 50ms
        
        all_passed = spot_reasonable and futures_reasonable
        
        if not spot_reasonable:
            print(f"  ❌ 现货年龄异常: {data_age_spot:.1f}ms (期望200ms左右)")
        if not futures_reasonable:
            print(f"  ❌ 期货年龄异常: {data_age_futures:.1f}ms (期望300ms左右)")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔥 时间戳修复验证 - 2025-08-01")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("时间戳整数一致性", test_timestamp_integer_consistency),
        ("跨交易所同步检查", test_cross_exchange_sync),
        ("数据年龄计算", test_data_age_calculation),
        ("OpportunityScanner修复", test_opportunity_scanner_fix),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        print("-" * 40)
        results[test_name] = test_func()
    
    # 生成测试报告
    print("\n📋 测试报告:")
    print("=" * 60)
    
    passed_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_count}/{total_count} 测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！时间戳修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
