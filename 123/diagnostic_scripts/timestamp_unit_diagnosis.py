#!/usr/bin/env python3
"""
🔥 时间戳单位不一致问题精确诊断脚本

诊断OpportunityScanner中第1904行的时间戳单位混用问题：
- current_time是毫秒级（第1872行）
- calculate_data_age期望current_time是秒级
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append('/root/myproject/123/66B 修复时间戳问题，但是全是错误/123')

def test_timestamp_unit_issue():
    """测试时间戳单位不一致问题"""
    print("🔍 时间戳单位不一致问题诊断")
    print("=" * 60)

    # 模拟OpportunityScanner中的问题（不依赖模块导入）
    current_time_ms = int(time.time() * 1000)  # 毫秒时间戳（模拟第1872行）
    print(f"📊 current_time (毫秒级): {current_time_ms}")

    # 模拟市场数据时间戳（毫秒级）
    spot_timestamp = 1754055479586  # 从日志中的实际值
    futures_timestamp = 1754055489526  # 从日志中的实际值

    print(f"📊 spot_timestamp: {spot_timestamp}")
    print(f"📊 futures_timestamp: {futures_timestamp}")

    # 模拟calculate_data_age函数的行为
    def mock_calculate_data_age(data_timestamp, current_time_param):
        """模拟calculate_data_age函数"""
        # 函数期望current_time_param是秒级
        if data_timestamp < 1e12:
            data_timestamp_ms = int(data_timestamp * 1000)
        else:
            data_timestamp_ms = int(data_timestamp)

        current_time_ms_internal = int(current_time_param * 1000)
        age_ms = abs(current_time_ms_internal - data_timestamp_ms)
        return age_ms / 1000.0

    # 第1904行的错误调用：传入毫秒级current_time给期望秒级的函数
    print("\n🔥 错误调用（第1904行）：")
    try:
        # 错误：传入毫秒级时间戳给期望秒级的函数
        data_age_spot_seconds = mock_calculate_data_age(spot_timestamp, current_time_ms)  # 错误！
        data_age_futures_seconds = mock_calculate_data_age(futures_timestamp, current_time_ms)  # 错误！

        print(f"❌ data_age_spot_seconds: {data_age_spot_seconds}")
        print(f"❌ data_age_futures_seconds: {data_age_futures_seconds}")

        # 转换为毫秒
        data_age_spot = data_age_spot_seconds * 1000
        data_age_futures = data_age_futures_seconds * 1000

        print(f"❌ data_age_spot (毫秒): {data_age_spot}")
        print(f"❌ data_age_futures (毫秒): {data_age_futures}")

        # 检查是否超过500ms阈值
        max_data_age = 500
        print(f"❌ spot数据是否过期 (>{max_data_age}ms): {data_age_spot > max_data_age}")
        print(f"❌ futures数据是否过期 (>{max_data_age}ms): {data_age_futures > max_data_age}")

    except Exception as e:
        print(f"❌ 错误调用异常: {e}")

    # 正确调用：传入秒级current_time
    print("\n✅ 正确调用：")
    try:
        current_time_seconds = current_time_ms / 1000  # 转换为秒级
        data_age_spot_seconds_correct = mock_calculate_data_age(spot_timestamp, current_time_seconds)
        data_age_futures_seconds_correct = mock_calculate_data_age(futures_timestamp, current_time_seconds)

        print(f"✅ current_time_seconds: {current_time_seconds}")
        print(f"✅ data_age_spot_seconds_correct: {data_age_spot_seconds_correct}")
        print(f"✅ data_age_futures_seconds_correct: {data_age_futures_seconds_correct}")

        # 转换为毫秒
        data_age_spot_correct = data_age_spot_seconds_correct * 1000
        data_age_futures_correct = data_age_futures_seconds_correct * 1000

        print(f"✅ data_age_spot_correct (毫秒): {data_age_spot_correct}")
        print(f"✅ data_age_futures_correct (毫秒): {data_age_futures_correct}")

        # 检查是否超过500ms阈值
        max_data_age = 500
        print(f"✅ spot数据是否过期 (>{max_data_age}ms): {data_age_spot_correct > max_data_age}")
        print(f"✅ futures数据是否过期 (>{max_data_age}ms): {data_age_futures_correct > max_data_age}")

    except Exception as e:
        print(f"❌ 正确调用异常: {e}")

def test_calculate_data_age_function():
    """测试calculate_data_age函数的行为"""
    print("\n🔍 calculate_data_age函数行为测试")
    print("=" * 60)

    # 模拟calculate_data_age函数（不依赖模块导入）
    def mock_calculate_data_age(data_timestamp, current_time_param):
        """模拟calculate_data_age函数的实际行为"""
        # 标准化数据时间戳为毫秒级
        if data_timestamp < 1e12:
            data_timestamp_ms = int(data_timestamp * 1000)
        else:
            data_timestamp_ms = int(data_timestamp)

        # 当前时间转换为毫秒级
        current_time_ms = int(current_time_param * 1000)

        # 计算年龄（毫秒），然后转换为秒
        age_ms = abs(current_time_ms - data_timestamp_ms)
        age_seconds = age_ms / 1000.0

        return age_seconds

    # 测试用例1：正常情况（秒级current_time）
    print("📊 测试用例1：正常情况")
    current_time_seconds = time.time()
    data_timestamp = int(current_time_seconds * 1000) - 1000  # 1秒前的毫秒时间戳

    age = mock_calculate_data_age(data_timestamp, current_time_seconds)
    print(f"  current_time_seconds: {current_time_seconds}")
    print(f"  data_timestamp: {data_timestamp}")
    print(f"  计算出的年龄: {age} 秒")
    print(f"  预期年龄: ~1.0 秒")

    # 测试用例2：错误情况（毫秒级current_time）
    print("\n📊 测试用例2：错误情况（毫秒级current_time）")
    current_time_ms = int(current_time_seconds * 1000)

    age_wrong = mock_calculate_data_age(data_timestamp, current_time_ms)
    print(f"  current_time_ms: {current_time_ms}")
    print(f"  data_timestamp: {data_timestamp}")
    print(f"  计算出的年龄: {age_wrong} 秒")
    print(f"  这就是问题所在！巨大的年龄值: {age_wrong/1000:.1f}k 秒")

def analyze_log_timestamps():
    """分析日志中的时间戳"""
    print("\n🔍 分析日志中的时间戳")
    print("=" * 60)
    
    # 从日志中提取的实际时间戳
    log_examples = [
        {
            "combo": "gate_spot_bybit_futures",
            "spot_timestamp": 1754055479586,
            "futures_timestamp": 1754055489526,
            "time_diff_ms": 9940
        },
        {
            "combo": "bybit_spot_okx_futures", 
            "spot_timestamp": 1754055489992,
            "futures_timestamp": 1754055489110,
            "time_diff_ms": 882
        }
    ]
    
    current_time = time.time()
    print(f"📊 当前时间: {current_time} 秒")
    print(f"📊 当前时间: {int(current_time * 1000)} 毫秒")
    
    for example in log_examples:
        print(f"\n📊 组合: {example['combo']}")
        print(f"  spot_timestamp: {example['spot_timestamp']}")
        print(f"  futures_timestamp: {example['futures_timestamp']}")
        print(f"  记录的时间差: {example['time_diff_ms']} ms")
        
        # 验证时间差计算
        actual_diff = abs(example['spot_timestamp'] - example['futures_timestamp'])
        print(f"  实际时间差: {actual_diff} ms")
        print(f"  计算是否正确: {'✅' if abs(actual_diff - example['time_diff_ms']) < 1 else '❌'}")
        
        # 检查时间戳是否合理（应该接近当前时间）
        spot_age = abs(current_time * 1000 - example['spot_timestamp']) / 1000
        futures_age = abs(current_time * 1000 - example['futures_timestamp']) / 1000
        
        print(f"  spot数据年龄: {spot_age:.1f} 秒")
        print(f"  futures数据年龄: {futures_age:.1f} 秒")
        
        # 这些时间戳看起来是未来的时间戳！
        if example['spot_timestamp'] > current_time * 1000:
            print(f"  ⚠️ spot时间戳是未来时间！")
        if example['futures_timestamp'] > current_time * 1000:
            print(f"  ⚠️ futures时间戳是未来时间！")

def main():
    """主函数"""
    print("🔥 OpportunityScanner时间戳单位不一致问题诊断")
    print("=" * 80)
    print(f"诊断时间: {datetime.now()}")
    print()
    
    try:
        # 测试时间戳单位问题
        test_timestamp_unit_issue()
        
        # 测试calculate_data_age函数
        test_calculate_data_age_function()
        
        # 分析日志时间戳
        analyze_log_timestamps()
        
        print("\n🎯 诊断结论:")
        print("=" * 60)
        print("1. ❌ OpportunityScanner第1904行存在时间戳单位混用问题")
        print("2. ❌ current_time是毫秒级，但calculate_data_age期望秒级")
        print("3. ❌ 这导致数据年龄计算错误，产生巨大负数")
        print("4. ❌ 错误的数据年龄导致所有数据被误判为'新鲜'")
        print("5. ❌ 同时时间戳不同步检查也可能受到影响")
        
        print("\n🔧 修复建议:")
        print("=" * 60)
        print("修改OpportunityScanner第1904-1905行：")
        print("  # 修复前（错误）：")
        print("  data_age_spot_seconds = calculate_data_age(spot_timestamp, current_time)")
        print("  # 修复后（正确）：")
        print("  data_age_spot_seconds = calculate_data_age(spot_timestamp, current_time / 1000)")
        
    except Exception as e:
        print(f"❌ 诊断过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
