#!/usr/bin/env python3
"""
🔥 时间戳浮点数问题精确诊断脚本

问题：日志显示 discarded_timestamp: 1754055467805.0 (浮点数)
正确：应该是 1754055467805 (整数)

这表明在时间戳处理链路中存在不当的浮点数转换
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append('/root/myproject/123/66B 修复时间戳问题，但是全是错误/123')

def analyze_timestamp_float_issue():
    """分析时间戳浮点数问题"""
    print("🔍 时间戳浮点数问题诊断")
    print("=" * 60)
    
    # 从日志中提取的问题时间戳
    problematic_timestamp = 1754055467805.0  # 浮点数！
    print(f"📊 问题时间戳: {problematic_timestamp}")
    print(f"📊 时间戳类型: {type(problematic_timestamp)}")
    print(f"📊 是否为浮点数: {isinstance(problematic_timestamp, float)}")
    
    # 正确的时间戳应该是整数
    correct_timestamp = int(problematic_timestamp)
    print(f"📊 正确时间戳: {correct_timestamp}")
    print(f"📊 正确类型: {type(correct_timestamp)}")
    
    # 分析可能的转换路径
    print("\n🔍 可能的错误转换路径分析:")
    
    # 路径1: 秒级时间戳 * 1000.0 (浮点数乘法)
    seconds_timestamp = 1754055467.805
    result1 = seconds_timestamp * 1000.0
    print(f"路径1: {seconds_timestamp} * 1000.0 = {result1} ({type(result1)})")
    
    # 路径2: 整数时间戳 / 1000 * 1000 (不必要的除法再乘法)
    original_ms = 1754055467805
    result2 = (original_ms / 1000) * 1000
    print(f"路径2: ({original_ms} / 1000) * 1000 = {result2} ({type(result2)})")
    
    # 路径3: 纳秒时间戳 / 1000000.0 (浮点数除法)
    nanoseconds = 1754055467805000000
    result3 = nanoseconds / 1000000.0
    print(f"路径3: {nanoseconds} / 1000000.0 = {result3} ({type(result3)})")
    
    print(f"\n🎯 匹配分析:")
    print(f"问题时间戳 {problematic_timestamp} 最可能来自路径2或路径3")

def test_unified_timestamp_processor():
    """测试统一时间戳处理器的行为"""
    print("\n🔍 统一时间戳处理器行为测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp, _normalize_timestamp_format
        
        # 测试各种输入
        test_cases = [
            ("整数毫秒", 1754055467805),
            ("浮点毫秒", 1754055467805.0),
            ("整数秒", 1754055467),
            ("浮点秒", 1754055467.805),
            ("纳秒", 1754055467805000000),
        ]
        
        for desc, input_ts in test_cases:
            try:
                result = ensure_milliseconds_timestamp(input_ts)
                print(f"{desc:12} | 输入: {input_ts:>20} ({type(input_ts).__name__:>5}) | 输出: {result:>15} ({type(result).__name__:>3})")
            except Exception as e:
                print(f"{desc:12} | 输入: {input_ts:>20} ({type(input_ts).__name__:>5}) | 错误: {e}")
                
    except ImportError as e:
        print(f"❌ 无法导入模块: {e}")
        
        # 模拟ensure_milliseconds_timestamp函数
        def mock_ensure_milliseconds_timestamp(timestamp):
            """模拟函数"""
            if timestamp is None:
                return int(time.time() * 1000)
            
            timestamp = float(timestamp)
            
            if timestamp <= 0:
                return int(time.time() * 1000)
            
            if timestamp < 1e10:  # 秒级
                return int(timestamp * 1000)  # 🔥 这里可能产生浮点数！
            elif timestamp < 1e13:  # 毫秒级
                return int(timestamp)
            else:  # 纳秒级
                return int(timestamp / 1000000)  # 🔥 这里可能产生浮点数！
        
        print("\n使用模拟函数测试:")
        test_cases = [
            ("整数毫秒", 1754055467805),
            ("浮点毫秒", 1754055467805.0),
            ("整数秒", 1754055467),
            ("浮点秒", 1754055467.805),
            ("纳秒", 1754055467805000000),
        ]
        
        for desc, input_ts in test_cases:
            try:
                result = mock_ensure_milliseconds_timestamp(input_ts)
                print(f"{desc:12} | 输入: {input_ts:>20} ({type(input_ts).__name__:>5}) | 输出: {result:>15} ({type(result).__name__:>3})")
            except Exception as e:
                print(f"{desc:12} | 输入: {input_ts:>20} ({type(input_ts).__name__:>5}) | 错误: {e}")

def analyze_okx_timestamp_extraction():
    """分析OKX时间戳提取问题"""
    print("\n🔍 OKX时间戳提取问题分析")
    print("=" * 60)
    
    # 从日志可以看出问题来自 'okx_ts_field'
    print("📊 日志显示: extraction_source': 'okx_ts_field'")
    print("📊 这表明问题出现在OKX时间戳字段提取过程中")
    
    # 模拟OKX可能的时间戳格式
    okx_timestamp_examples = [
        ("字符串毫秒", "1754055467805"),
        ("字符串秒", "1754055467.805"),
        ("整数毫秒", 1754055467805),
        ("浮点毫秒", 1754055467805.0),
        ("纳秒", 1754055467805000000),
    ]
    
    print("\n可能的OKX时间戳格式:")
    for desc, ts in okx_timestamp_examples:
        print(f"{desc:12} | 值: {ts} | 类型: {type(ts).__name__}")
        
        # 模拟转换过程
        try:
            if isinstance(ts, str):
                if '.' in ts:
                    # 可能是秒级浮点字符串
                    float_val = float(ts)
                    if float_val < 1e10:
                        result = float_val * 1000  # 🔥 产生浮点数！
                    else:
                        result = float_val
                else:
                    result = int(ts)
            else:
                result = ts
                
            print(f"             | 转换后: {result} | 类型: {type(result).__name__}")
            
        except Exception as e:
            print(f"             | 转换错误: {e}")

def check_timestamp_processor_chain():
    """检查时间戳处理链路"""
    print("\n🔍 时间戳处理链路检查")
    print("=" * 60)
    
    print("🔗 可能的处理链路:")
    print("1. OKX WebSocket数据 → _extract_server_timestamp_for_monitoring")
    print("2. _extract_server_timestamp_for_monitoring → _normalize_timestamp_format")
    print("3. _normalize_timestamp_format → ensure_milliseconds_timestamp")
    print("4. ensure_milliseconds_timestamp → 最终时间戳")
    
    print("\n🎯 问题定位:")
    print("- 日志显示 extraction_source: 'okx_ts_field'")
    print("- 这表明问题在步骤1或步骤2")
    print("- 浮点数时间戳说明存在不当的浮点运算")
    
    print("\n🔧 需要检查的代码位置:")
    print("1. websocket/unified_timestamp_processor.py 中的 _extract_server_timestamp_for_monitoring")
    print("2. websocket/unified_timestamp_processor.py 中的 _normalize_timestamp_format")
    print("3. OKX WebSocket客户端中的时间戳字段提取")

def main():
    """主函数"""
    print("🔥 时间戳浮点数问题精确诊断")
    print("=" * 80)
    print(f"诊断时间: {datetime.now()}")
    print()
    
    try:
        # 分析时间戳浮点数问题
        analyze_timestamp_float_issue()
        
        # 测试统一时间戳处理器
        test_unified_timestamp_processor()
        
        # 分析OKX时间戳提取
        analyze_okx_timestamp_extraction()
        
        # 检查处理链路
        check_timestamp_processor_chain()
        
        print("\n🎯 诊断结论:")
        print("=" * 60)
        print("1. ❌ 时间戳被错误地转换为浮点数")
        print("2. ❌ 问题出现在OKX时间戳提取过程中 (okx_ts_field)")
        print("3. ❌ 可能的原因：不当的浮点运算或字符串转换")
        print("4. ❌ 需要确保所有时间戳处理都返回整数")
        
        print("\n🔧 修复方向:")
        print("=" * 60)
        print("1. 检查 _extract_server_timestamp_for_monitoring 中的OKX处理")
        print("2. 检查 _normalize_timestamp_format 中的转换逻辑")
        print("3. 确保所有时间戳转换都使用 int() 而不是保留浮点数")
        print("4. 特别注意秒级转毫秒级的乘法运算")
        
    except Exception as e:
        print(f"❌ 诊断过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
