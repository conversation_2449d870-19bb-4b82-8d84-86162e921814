#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ 测试与实盘环境一致性验证器
确保修复后的时间戳功能在测试和实盘环境中表现完全一致
"""

import asyncio
import time
import logging
import sys
import os
import json
from typing import Dict, Any, List, Tuple

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 导入必要模块
try:
    from websocket.unified_timestamp_processor import (
        get_timestamp_processor, 
        get_synced_timestamp,
        initialize_all_timestamp_processors,
        check_all_timestamp_sync_health
    )
    from websocket.performance_monitor import record_message_latency
    from core.opportunity_scanner import OpportunityScanner
    from core.unified_order_spread_calculator import UnifiedOrderSpreadCalculator
except ImportError as e:
    # 尝试直接导入
    import sys
    import os
    websocket_path = os.path.join(parent_dir, 'websocket')
    core_path = os.path.join(parent_dir, 'core')
    sys.path.insert(0, websocket_path)
    sys.path.insert(0, core_path)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestProdConsistencyValidator:
    """测试与实盘环境一致性验证器"""
    
    def __init__(self):
        self.exchanges = ["gate", "bybit", "okx"]
        self.test_results = []
        
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行完整的一致性验证"""
        logger.info("=" * 100)
        logger.info("🏛️ 开始测试与实盘环境一致性验证")
        logger.info("=" * 100)
        
        # 验证阶段
        stages = [
            ("🔧 阶段1：模拟实盘网络环境", self.test_network_environment_simulation),
            ("⚡ 阶段2：时间戳同步健康检查", self.test_timestamp_sync_health),
            ("🔄 阶段3：实际数据流处理验证", self.test_data_flow_processing),
            ("🎯 阶段4：错误处理和恢复验证", self.test_error_handling_recovery),
            ("💪 阶段5：长期稳定性验证", self.test_long_term_stability),
            ("🏁 阶段6：完整套利流程验证", self.test_complete_arbitrage_flow)
        ]
        
        passed_stages = 0
        total_stages = len(stages)
        
        for stage_name, stage_func in stages:
            logger.info(f"\n{stage_name}")
            logger.info("-" * 80)
            
            try:
                result = await stage_func()
                if result:
                    logger.info(f"✅ {stage_name}: 通过")
                    passed_stages += 1
                else:
                    logger.error(f"❌ {stage_name}: 失败")
            except Exception as e:
                logger.error(f"❌ {stage_name}: 异常 - {e}")
        
        # 生成最终报告
        success_rate = (passed_stages / total_stages) * 100
        final_result = {
            "timestamp": int(time.time() * 1000),
            "total_stages": total_stages,
            "passed_stages": passed_stages,
            "success_rate": success_rate,
            "overall_status": "CONSISTENT" if success_rate == 100 else "INCONSISTENT",
            "test_results": self.test_results
        }
        
        logger.info("=" * 100)
        logger.info("📊 测试与实盘环境一致性验证最终报告")
        logger.info("=" * 100)
        logger.info(f"总阶段数: {total_stages}")
        logger.info(f"通过阶段: {passed_stages}")
        logger.info(f"成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            logger.info("🎉 测试与实盘环境100%一致！")
            logger.info("✅ 确认：不会再出现大量操蛋的错误！")
        else:
            logger.error("🚨 测试与实盘环境存在不一致性！")
            
        return final_result
    
    async def test_network_environment_simulation(self) -> bool:
        """模拟实盘网络环境"""
        try:
            logger.info("🌐 模拟真实网络环境条件...")
            
            # 1. 模拟网络延迟
            network_delays = [50, 100, 200, 500, 1000]  # 毫秒
            delay_results = []
            
            for delay_ms in network_delays:
                logger.info(f"测试网络延迟: {delay_ms}ms...")
                
                # 模拟延迟环境下的时间戳生成
                start_time = time.time()
                await asyncio.sleep(delay_ms / 1000.0)  # 模拟网络延迟
                
                timestamps = {}
                for exchange in self.exchanges:
                    ts = get_synced_timestamp(exchange, None)
                    timestamps[exchange] = ts
                
                end_time = time.time()
                actual_delay = (end_time - start_time) * 1000
                
                # 验证在延迟环境下时间戳仍然合理
                current_time = time.time() * 1000
                max_age = max(abs(ts - current_time) for ts in timestamps.values())
                
                delay_results.append({
                    "target_delay_ms": delay_ms,
                    "actual_delay_ms": actual_delay,
                    "max_timestamp_age_ms": max_age,
                    "timestamps": timestamps,
                    "acceptable": max_age < 10000  # 10秒容忍度
                })
                
                logger.info(f"  延迟{delay_ms}ms: 最大时间戳年龄={max_age:.1f}ms")
            
            # 2. 模拟网络中断和恢复
            logger.info("🔌 模拟网络中断和恢复...")
            
            # 初始化时间同步
            await initialize_all_timestamp_processors(force_sync=True)
            
            # 模拟网络中断期间的行为
            interrupt_results = []
            for duration in [1, 5, 10]:  # 中断1秒、5秒、10秒
                logger.info(f"模拟网络中断 {duration}s...")
                
                # 记录中断前状态
                before_timestamps = {}
                for exchange in self.exchanges:
                    before_timestamps[exchange] = get_synced_timestamp(exchange, None)
                
                # 模拟中断
                await asyncio.sleep(duration)
                
                # 记录中断后状态
                after_timestamps = {}
                for exchange in self.exchanges:
                    after_timestamps[exchange] = get_synced_timestamp(exchange, None)
                
                # 验证系统是否正常工作
                time_progression = all(
                    after_timestamps[ex] > before_timestamps[ex] 
                    for ex in self.exchanges
                )
                
                interrupt_results.append({
                    "duration_seconds": duration,
                    "before_timestamps": before_timestamps,
                    "after_timestamps": after_timestamps,
                    "time_progression_normal": time_progression
                })
                
                logger.info(f"  中断{duration}s: 时间推进正常={time_progression}")
            
            # 3. 验证结果
            network_test_passed = (
                all(r["acceptable"] for r in delay_results) and
                all(r["time_progression_normal"] for r in interrupt_results)
            )
            
            self.test_results.append({
                "test": "network_environment_simulation",
                "passed": network_test_passed,
                "delay_results": delay_results,
                "interrupt_results": interrupt_results,
                "details": "网络环境模拟测试"
            })
            
            return network_test_passed
            
        except Exception as e:
            logger.error(f"网络环境模拟测试异常: {e}")
            return False
    
    async def test_timestamp_sync_health(self) -> bool:
        """时间戳同步健康检查"""
        try:
            logger.info("💊 执行时间戳同步健康检查...")
            
            # 检查所有交易所的同步健康状态
            health_status = await check_all_timestamp_sync_health()
            
            logger.info("健康状态报告:")
            for exchange, status in health_status.items():
                health_level = status.get("health_level", "UNKNOWN")
                is_healthy = status.get("is_healthy", False)
                
                logger.info(f"  {exchange.upper()}: {health_level} (健康={is_healthy})")
                
                if "error" in status:
                    logger.warning(f"    错误: {status['error']}")
                else:
                    sync_status = status.get("time_synced", False)
                    offset = status.get("time_offset_ms", 0)
                    logger.info(f"    同步={sync_status}, 偏移={offset}ms")
            
            # 验证健康标准
            critical_count = sum(1 for status in health_status.values() 
                               if status.get("health_level") == "CRITICAL")
            error_count = sum(1 for status in health_status.values() 
                            if status.get("health_level") == "ERROR")
            
            # 允许部分交易所时间同步失败，因为系统有fallback机制
            health_acceptable = critical_count == 0 and error_count == 0
            
            self.test_results.append({
                "test": "timestamp_sync_health",
                "passed": health_acceptable,
                "health_status": health_status,
                "critical_count": critical_count,
                "error_count": error_count,
                "details": "时间戳同步健康检查"
            })
            
            return health_acceptable
            
        except Exception as e:
            logger.error(f"时间戳同步健康检查异常: {e}")
            return False
    
    async def test_data_flow_processing(self) -> bool:
        """实际数据流处理验证"""
        try:
            logger.info("📊 测试真实数据流处理...")
            
            # 模拟真实的WebSocket数据流
            test_data_streams = []
            
            # 生成模拟数据
            current_time = time.time()
            for i in range(100):  # 100个数据点
                # 为每个交易所生成不同格式的数据
                gate_data = {
                    "s": "BTC_USDT",
                    "t": int((current_time - i * 0.1) * 1000),  # 每100ms一个数据点
                    "asks": [[50000 + i, 1.0]],
                    "bids": [[49999 - i, 1.0]]
                }
                
                bybit_data = {
                    "topic": "orderbook.200.BTCUSDT",
                    "ts": int((current_time - i * 0.1) * 1000),
                    "data": {
                        "a": [[str(50001 + i), str(1.0)]],
                        "b": [[str(49998 - i), str(1.0)]]
                    }
                }
                
                okx_data = {
                    "arg": {"instId": "BTC-USDT", "channel": "books"},
                    "data": [{
                        "ts": str(int((current_time - i * 0.1) * 1000)),
                        "asks": [[str(50002 + i), str(1.0)]],
                        "bids": [[str(49997 - i), str(1.0)]]
                    }]
                }
                
                test_data_streams.append({
                    "gate": gate_data,
                    "bybit": bybit_data,
                    "okx": okx_data,
                    "expected_time": current_time - i * 0.1
                })
            
            # 处理数据流并验证时间戳
            processing_results = []
            
            for i, data_set in enumerate(test_data_streams[:10]):  # 测试前10个数据点
                batch_results = {}
                
                for exchange in self.exchanges:
                    try:
                        # 处理数据并获取时间戳
                        ts = get_synced_timestamp(exchange, data_set[exchange])
                        
                        # 验证时间戳合理性
                        expected_time_ms = data_set["expected_time"] * 1000
                        time_diff = abs(ts - expected_time_ms)
                        
                        batch_results[exchange] = {
                            "timestamp": ts,
                            "expected": expected_time_ms,
                            "diff_ms": time_diff,
                            "reasonable": time_diff < 5000  # 5秒容忍度
                        }
                        
                    except Exception as e:
                        batch_results[exchange] = {
                            "error": str(e),
                            "reasonable": False
                        }
                
                processing_results.append({
                    "batch": i,
                    "results": batch_results
                })
                
                if i < 3:  # 显示前3个批次的详细结果
                    logger.info(f"  批次{i}: " + 
                              ", ".join(f"{ex}={r.get('diff_ms', 'ERR'):.0f}ms" 
                                      for ex, r in batch_results.items()))
            
            # 验证处理成功率
            total_processed = len(processing_results) * len(self.exchanges)
            successful_processed = sum(
                sum(1 for r in batch["results"].values() if r.get("reasonable", False))
                for batch in processing_results
            )
            
            success_rate = successful_processed / total_processed if total_processed > 0 else 0
            processing_passed = success_rate >= 0.8  # 80%成功率阈值
            
            logger.info(f"数据流处理成功率: {success_rate:.1%} ({successful_processed}/{total_processed})")
            
            self.test_results.append({
                "test": "data_flow_processing",
                "passed": processing_passed,
                "success_rate": success_rate,
                "total_processed": total_processed,
                "successful_processed": successful_processed,
                "processing_results": processing_results[:5],  # 只保存前5个结果
                "details": "实际数据流处理验证"
            })
            
            return processing_passed
            
        except Exception as e:
            logger.error(f"数据流处理验证异常: {e}")
            return False
    
    async def test_error_handling_recovery(self) -> bool:
        """错误处理和恢复验证"""
        try:
            logger.info("🔧 测试错误处理和恢复机制...")
            
            error_scenarios = [
                ("无效时间戳数据", {"timestamp": "invalid"}),
                ("空数据", {}),
                ("None数据", None),
                ("超大时间戳", {"ts": 9999999999999999}),
                ("负数时间戳", {"timestamp": -1000}),
                ("字符串时间戳", {"ts": "1640995200000"}),
            ]
            
            recovery_results = []
            
            for scenario_name, test_data in error_scenarios:
                logger.info(f"测试场景: {scenario_name}")
                scenario_results = {}
                
                for exchange in self.exchanges:
                    try:
                        # 尝试处理错误数据
                        ts = get_synced_timestamp(exchange, test_data)
                        
                        # 验证返回的时间戳是否合理（应该是当前时间）
                        current_time = time.time() * 1000
                        age = abs(ts - current_time)
                        
                        recovery_successful = (
                            isinstance(ts, int) and  # 返回整数时间戳
                            ts > 1e12 and          # 毫秒级时间戳
                            age < 5000             # 不超过5秒
                        )
                        
                        scenario_results[exchange] = {
                            "recovered": recovery_successful,
                            "timestamp": ts,
                            "age_ms": age
                        }
                        
                    except Exception as e:
                        # 异常也算恢复失败
                        scenario_results[exchange] = {
                            "recovered": False,
                            "error": str(e)
                        }
                
                recovery_rate = sum(1 for r in scenario_results.values() 
                                  if r.get("recovered", False)) / len(self.exchanges)
                
                recovery_results.append({
                    "scenario": scenario_name,
                    "recovery_rate": recovery_rate,
                    "results": scenario_results
                })
                
                logger.info(f"  恢复率: {recovery_rate:.1%}")
            
            # 验证整体恢复能力
            avg_recovery_rate = sum(r["recovery_rate"] for r in recovery_results) / len(recovery_results)
            recovery_passed = avg_recovery_rate >= 0.8  # 80%恢复率阈值
            
            logger.info(f"平均恢复率: {avg_recovery_rate:.1%}")
            
            self.test_results.append({
                "test": "error_handling_recovery",
                "passed": recovery_passed,
                "avg_recovery_rate": avg_recovery_rate,
                "recovery_results": recovery_results,
                "details": "错误处理和恢复验证"
            })
            
            return recovery_passed
            
        except Exception as e:
            logger.error(f"错误处理和恢复验证异常: {e}")
            return False
    
    async def test_long_term_stability(self) -> bool:
        """长期稳定性验证"""
        try:
            logger.info("⏰ 测试长期稳定性...")
            
            # 持续运行测试
            duration_seconds = 30  # 30秒持续测试
            sample_interval = 1    # 每秒采样
            
            stability_samples = []
            start_time = time.time()
            
            logger.info(f"开始{duration_seconds}秒稳定性测试...")
            
            while time.time() - start_time < duration_seconds:
                sample_time = time.time()
                sample_timestamps = {}
                
                # 采集所有交易所的时间戳
                for exchange in self.exchanges:
                    try:
                        ts = get_synced_timestamp(exchange, None)
                        sample_timestamps[exchange] = ts
                    except Exception as e:
                        sample_timestamps[exchange] = None
                        logger.warning(f"{exchange} 时间戳获取失败: {e}")
                
                # 计算时间戳差异
                valid_timestamps = [ts for ts in sample_timestamps.values() if ts is not None]
                if len(valid_timestamps) >= 2:
                    max_diff = max(valid_timestamps) - min(valid_timestamps)
                else:
                    max_diff = None
                
                stability_samples.append({
                    "sample_time": sample_time,
                    "timestamps": sample_timestamps,
                    "max_diff_ms": max_diff,
                    "valid_count": len(valid_timestamps)
                })
                
                # 每5秒报告一次进度
                elapsed = time.time() - start_time
                if int(elapsed) % 5 == 0 and len(stability_samples) % 5 == 1:
                    logger.info(f"  进度: {elapsed:.0f}s/{duration_seconds}s")
                
                await asyncio.sleep(sample_interval)
            
            # 分析稳定性
            total_samples = len(stability_samples)
            valid_samples = sum(1 for s in stability_samples if s["valid_count"] >= 2)
            
            if valid_samples > 0:
                avg_max_diff = sum(s["max_diff_ms"] for s in stability_samples 
                                 if s["max_diff_ms"] is not None) / valid_samples
                max_max_diff = max(s["max_diff_ms"] for s in stability_samples 
                                 if s["max_diff_ms"] is not None)
            else:
                avg_max_diff = float('inf')
                max_max_diff = float('inf')
            
            availability_rate = valid_samples / total_samples
            
            # 稳定性标准
            stability_passed = (
                availability_rate >= 0.95 and  # 95%可用率
                avg_max_diff < 1000 and        # 平均差异小于1秒
                max_max_diff < 5000            # 最大差异小于5秒
            )
            
            logger.info(f"稳定性测试结果:")
            logger.info(f"  样本数: {total_samples}")
            logger.info(f"  有效样本: {valid_samples} ({availability_rate:.1%})")
            logger.info(f"  平均最大差异: {avg_max_diff:.1f}ms")
            logger.info(f"  最大差异: {max_max_diff:.1f}ms")
            
            self.test_results.append({
                "test": "long_term_stability",
                "passed": stability_passed,
                "duration_seconds": duration_seconds,
                "total_samples": total_samples,
                "valid_samples": valid_samples,
                "availability_rate": availability_rate,
                "avg_max_diff_ms": avg_max_diff,
                "max_max_diff_ms": max_max_diff,
                "details": "长期稳定性验证"
            })
            
            return stability_passed
            
        except Exception as e:
            logger.error(f"长期稳定性验证异常: {e}")
            return False
    
    async def test_complete_arbitrage_flow(self) -> bool:
        """完整套利流程验证"""
        try:
            logger.info("🏆 测试完整套利流程...")
            
            # 模拟完整的套利机会检测和处理流程
            arbitrage_scenarios = [
                {
                    "name": "标准套利机会",
                    "gate_spot": {"symbol": "BTC_USDT", "timestamp": time.time() - 0.1, "best_bid": 50000, "best_ask": 50001},
                    "bybit_futures": {"symbol": "BTCUSDT", "ts": int((time.time() - 0.15) * 1000), "best_bid": 50100, "best_ask": 50101},
                    "expected_spread": 2.0  # 约2%的价差
                },
                {
                    "name": "时间戳不同步场景",
                    "gate_spot": {"symbol": "ETH_USDT", "t": int((time.time() - 2.0) * 1000), "best_bid": 3000, "best_ask": 3001},
                    "okx_futures": {"instId": "ETH-USDT", "ts": str(int((time.time() - 0.1) * 1000)), "best_bid": 3050, "best_ask": 3051},
                    "expected_spread": 1.6  # 约1.6%的价差
                },
                {
                    "name": "网络延迟场景",
                    "bybit_spot": {"symbol": "SOLUSDT", "T": int((time.time() - 1.0) * 1000), "best_bid": 150, "best_ask": 150.1},
                    "gate_futures": {"s": "SOL_USDT", "timestamp": time.time() - 0.8, "best_bid": 152, "best_ask": 152.1},
                    "expected_spread": 1.3  # 约1.3%的价差
                }
            ]
            
            flow_results = []
            
            for scenario in arbitrage_scenarios:
                logger.info(f"测试场景: {scenario['name']}")
                
                try:
                    # 1. 时间戳处理和验证
                    timestamp_results = {}
                    
                    for key, data in scenario.items():
                        if key.endswith("_spot") or key.endswith("_futures"):
                            exchange = key.split("_")[0]
                            ts = get_synced_timestamp(exchange, data)
                            
                            # 计算数据年龄
                            current_time = time.time() * 1000
                            age = abs(ts - current_time)
                            
                            timestamp_results[key] = {
                                "timestamp": ts,
                                "age_ms": age,
                                "fresh": age < 2000  # 2秒新鲜度阈值
                            }
                    
                    # 2. 价差计算（简化版）
                    spread_calculated = True  # 简化，实际会更复杂
                    
                    # 3. 时间同步验证
                    timestamps = [r["timestamp"] for r in timestamp_results.values()]
                    if len(timestamps) >= 2:
                        max_time_diff = max(timestamps) - min(timestamps)
                        time_sync_ok = max_time_diff < 800  # 800ms同步阈值
                    else:
                        time_sync_ok = False
                    
                    # 4. 整体流程评估
                    all_fresh = all(r["fresh"] for r in timestamp_results.values())
                    flow_success = all_fresh and spread_calculated and time_sync_ok
                    
                    flow_results.append({
                        "scenario": scenario["name"],
                        "success": flow_success,
                        "timestamp_results": timestamp_results,
                        "max_time_diff_ms": max_time_diff if len(timestamps) >= 2 else None,
                        "all_fresh": all_fresh,
                        "time_sync_ok": time_sync_ok
                    })
                    
                    logger.info(f"  结果: {'成功' if flow_success else '失败'}")
                    if not flow_success:
                        if not all_fresh:
                            logger.warning(f"    数据不新鲜")
                        if not time_sync_ok:
                            logger.warning(f"    时间不同步 (差异{max_time_diff:.1f}ms)")
                    
                except Exception as e:
                    flow_results.append({
                        "scenario": scenario["name"],
                        "success": False,
                        "error": str(e)
                    })
                    logger.error(f"  场景失败: {e}")
            
            # 验证整体流程成功率
            successful_flows = sum(1 for r in flow_results if r["success"])
            flow_success_rate = successful_flows / len(flow_results)
            flow_passed = flow_success_rate >= 0.8  # 80%成功率阈值
            
            logger.info(f"套利流程成功率: {flow_success_rate:.1%} ({successful_flows}/{len(flow_results)})")
            
            self.test_results.append({
                "test": "complete_arbitrage_flow",
                "passed": flow_passed,
                "success_rate": flow_success_rate,
                "successful_flows": successful_flows,
                "total_flows": len(flow_results),
                "flow_results": flow_results,
                "details": "完整套利流程验证"
            })
            
            return flow_passed
            
        except Exception as e:
            logger.error(f"完整套利流程验证异常: {e}")
            return False

async def main():
    """主函数"""
    validator = TestProdConsistencyValidator()
    result = await validator.run_comprehensive_validation()
    
    # 保存测试结果
    report_path = "diagnostic_results/test_prod_consistency_validation.json"
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📄 详细测试报告已保存: {report_path}")
    
    return result["overall_status"] == "CONSISTENT"

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        sys.exit(1)