{"timestamp": 1754043516, "tests": {}, "issues_found": [], "api_calls_made": 6, "real_api_responses": [{"exchange": "gate", "url": "https://api.gateio.ws/api/v4/spot/time", "status": 200, "latency_ms": 36.94748878479004, "response_data": {"server_time": 1754043517134}, "real_api": true}, {"exchange": "bybit", "url": "https://api.bybit.com/v5/market/time", "status": 200, "latency_ms": 81.38155937194824, "response_data": {"retCode": 0, "retMsg": "OK", "result": {"timeSecond": "1754043517", "timeNano": "1754043517185314031"}, "retExtInfo": {}, "time": 1754043517185}, "real_api": true}, {"exchange": "okx", "url": "https://www.okx.com/api/v5/public/time", "status": 200, "latency_ms": 95.73149681091309, "response_data": {"code": "0", "data": [{"ts": "1754043517275"}], "msg": ""}, "real_api": true}]}