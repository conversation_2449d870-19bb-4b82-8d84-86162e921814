#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳问题精确诊断脚本
基于深度分析的发现，精准定位时间戳和WebSocket问题根本原因

发现的问题：
1. OKX时间戳过期问题：WebSocket数据中的ts字段是历史时间戳，不是实时数据
2. 跨交易所时间戳不同步：Gate和OKX时间差达3.75秒
3. Gate静默断开：多个连接60-74秒静默断开
4. 数据流不均衡：D组合(7459)正常，其他组合(135-399)数据大量丢失
"""

import asyncio
import logging
import time
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter

# 设置基础路径
BASE_DIR = "/root/myproject/123/66B 修复时间戳问题，但是全是错误/123"
LOG_DIR = os.path.join(BASE_DIR, "logs")

class TimestampDiagnosisResult:
    """诊断结果数据结构"""
    def __init__(self):
        self.timestamp_issues = []
        self.silent_disconnects = []
        self.cross_exchange_sync_issues = []
        self.data_distribution_issues = {}
        self.recommendations = []
        self.severity = "UNKNOWN"

class TimestampIssuesDiagnosis:
    """时间戳问题诊断器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.result = TimestampDiagnosisResult()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger("timestamp_diagnosis")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger

    async def diagnose_all_issues(self) -> TimestampDiagnosisResult:
        """执行全面诊断"""
        self.logger.info("🔍 开始执行时间戳问题精确诊断...")
        
        # 1. 分析时间戳过期问题
        await self._diagnose_timestamp_expiration()
        
        # 2. 分析静默断开问题
        await self._diagnose_silent_disconnections()
        
        # 3. 分析跨交易所时间戳同步问题
        await self._diagnose_cross_exchange_sync()
        
        # 4. 分析数据分布不均衡问题
        await self._diagnose_data_distribution()
        
        # 5. 评估整体严重性并生成建议
        await self._evaluate_severity_and_recommendations()
        
        self.logger.info("✅ 诊断完成")
        return self.result

    async def _diagnose_timestamp_expiration(self):
        """诊断时间戳过期问题"""
        self.logger.info("📊 分析时间戳过期数据丢弃...")
        
        perf_log_path = os.path.join(LOG_DIR, "websocket_performance_20250801.log")
        if not os.path.exists(perf_log_path):
            self.logger.warning(f"性能日志文件不存在: {perf_log_path}")
            return
            
        timestamp_issues = []
        exchange_stats = defaultdict(list)
        
        with open(perf_log_path, 'r', encoding='utf-8') as f:
            for line in f:
                if "时间戳过期数据丢弃" in line or "数据新鲜度检查失败" in line:
                    try:
                        # 解析日志行
                        parts = line.strip().split(" | ")
                        if len(parts) >= 2:
                            timestamp_str = parts[0].split()[0] + " " + parts[0].split()[1]
                            data_str = parts[1]
                            
                            # 解析JSON数据
                            data = json.loads(data_str.replace("'", '"'))
                            
                            exchange = data.get('exchange', 'unknown')
                            age_ms = data.get('timestamp_age_ms', 0)
                            discarded_ts = data.get('discarded_timestamp', 0)
                            
                            timestamp_issues.append({
                                'time': timestamp_str,
                                'exchange': exchange,
                                'age_ms': age_ms,
                                'discarded_timestamp': discarded_ts,
                                'severity': 'HIGH' if age_ms > 60000 else 'MEDIUM'
                            })
                            
                            exchange_stats[exchange].append(age_ms)
                            
                    except Exception as e:
                        continue
        
        # 统计分析
        for exchange, ages in exchange_stats.items():
            if ages:
                avg_age = sum(ages) / len(ages)
                max_age = max(ages)
                count = len(ages)
                
                issue = {
                    'type': 'timestamp_expiration',
                    'exchange': exchange,
                    'count': count,
                    'avg_age_ms': round(avg_age, 1),
                    'max_age_ms': round(max_age, 1),
                    'severity': 'CRITICAL' if max_age > 90000 else 'HIGH' if max_age > 60000 else 'MEDIUM'
                }
                
                self.result.timestamp_issues.append(issue)
                self.logger.info(f"🚨 {exchange.upper()}时间戳过期: {count}次, 平均{avg_age/1000:.1f}秒, 最大{max_age/1000:.1f}秒")

    async def _diagnose_silent_disconnections(self):
        """诊断静默断开问题"""
        self.logger.info("📊 分析静默断开问题...")
        
        silent_log_path = os.path.join(LOG_DIR, "websocket_silent_disconnect_20250801.log")
        if not os.path.exists(silent_log_path):
            self.logger.warning(f"静默断开日志文件不存在: {silent_log_path}")
            return
            
        with open(silent_log_path, 'r', encoding='utf-8') as f:
            for line in f:
                if "检测到静默断开" in line:
                    try:
                        parts = line.strip().split(" | ")
                        if len(parts) >= 2:
                            timestamp_str = parts[0].split()[0] + " " + parts[0].split()[1]
                            data_str = parts[1]
                            
                            data = json.loads(data_str.replace("'", '"'))
                            
                            disconnect = {
                                'time': timestamp_str,
                                'exchange': 'gate',  # 从日志看都是gate
                                'duration': data.get('silent_duration', 0),
                                'key': data.get('key', ''),
                                'last_update': data.get('last_update_time', 0),
                                'severity': 'HIGH' if data.get('silent_duration', 0) > 60 else 'MEDIUM'
                            }
                            
                            self.result.silent_disconnects.append(disconnect)
                            self.logger.info(f"🔇 Gate静默断开: {disconnect['key']}, 持续{disconnect['duration']:.1f}秒")
                            
                    except Exception as e:
                        continue

    async def _diagnose_cross_exchange_sync(self):
        """诊断跨交易所时间戳同步问题"""
        self.logger.info("📊 分析跨交易所时间戳同步问题...")
        
        perf_log_path = os.path.join(LOG_DIR, "websocket_performance_20250801.log")
        if not os.path.exists(perf_log_path):
            return
            
        sync_issues = []
        
        with open(perf_log_path, 'r', encoding='utf-8') as f:
            for line in f:
                if "跨交易所时间戳不同步" in line:
                    try:
                        parts = line.strip().split(" | ")
                        if len(parts) >= 2:
                            data_str = parts[1]
                            data = json.loads(data_str.replace("'", '"'))
                            
                            sync_issue = {
                                'exchange1': data.get('exchange1', ''),
                                'exchange2': data.get('exchange2', ''),
                                'time_diff_ms': data.get('time_diff_ms', 0),
                                'timestamp1': data.get('timestamp1', 0),
                                'timestamp2': data.get('timestamp2', 0),
                                'severity': 'CRITICAL' if data.get('time_diff_ms', 0) > 5000 else 'HIGH'
                            }
                            
                            sync_issues.append(sync_issue)
                            
                    except Exception as e:
                        continue
        
        # 分析同步问题模式
        exchange_pairs = defaultdict(list)
        for issue in sync_issues:
            pair_key = f"{issue['exchange1']}-{issue['exchange2']}"
            exchange_pairs[pair_key].append(issue['time_diff_ms'])
        
        for pair, time_diffs in exchange_pairs.items():
            if time_diffs:
                avg_diff = sum(time_diffs) / len(time_diffs)
                max_diff = max(time_diffs)
                count = len(time_diffs)
                
                sync_issue = {
                    'type': 'cross_exchange_sync',
                    'exchange_pair': pair,
                    'count': count,
                    'avg_diff_ms': round(avg_diff, 1),
                    'max_diff_ms': round(max_diff, 1),
                    'severity': 'CRITICAL' if max_diff > 5000 else 'HIGH'
                }
                
                self.result.cross_exchange_sync_issues.append(sync_issue)
                self.logger.info(f"⏰ {pair}时间戳不同步: {count}次, 平均{avg_diff:.1f}ms, 最大{max_diff:.1f}ms")

    async def _diagnose_data_distribution(self):
        """诊断数据分布不均衡问题"""
        self.logger.info("📊 分析数据分布不均衡问题...")
        
        prices_log_path = os.path.join(LOG_DIR, "websocket_prices.log")
        if not os.path.exists(prices_log_path):
            self.logger.warning(f"价格日志文件不存在: {prices_log_path}")
            return
        
        # 统计各组合的数据量
        combo_counts = Counter()
        
        with open(prices_log_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 查找组合标识 [A], [B], [C], [D], [E], [F]
                for combo in ['[A]', '[B]', '[C]', '[D]', '[E]', '[F]']:
                    if combo in line:
                        combo_counts[combo] += 1
                        break
        
        if combo_counts:
            total_records = sum(combo_counts.values())
            max_count = max(combo_counts.values())
            min_count = min(combo_counts.values()) if combo_counts else 0
            
            # 计算不均衡比例
            imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
            
            distribution_issue = {
                'type': 'data_distribution_imbalance',
                'total_records': total_records,
                'combo_counts': dict(combo_counts),
                'max_count': max_count,
                'min_count': min_count,
                'imbalance_ratio': round(imbalance_ratio, 1),
                'severity': 'CRITICAL' if imbalance_ratio > 20 else 'HIGH' if imbalance_ratio > 10 else 'MEDIUM'
            }
            
            self.result.data_distribution_issues = distribution_issue
            
            self.logger.info(f"📈 数据分布统计:")
            for combo, count in sorted(combo_counts.items()):
                percentage = (count / total_records) * 100
                self.logger.info(f"   {combo}: {count:4d}条 ({percentage:5.1f}%)")
            
            self.logger.info(f"🚨 数据不均衡比例: {imbalance_ratio:.1f}:1 (最多/最少)")

    async def _evaluate_severity_and_recommendations(self):
        """评估整体严重性并生成修复建议"""
        self.logger.info("📋 生成修复建议...")
        
        # 计算整体严重性
        critical_count = 0
        high_count = 0
        
        # 统计各类问题严重性
        for issue in self.result.timestamp_issues:
            if issue['severity'] == 'CRITICAL':
                critical_count += 1
            elif issue['severity'] == 'HIGH':
                high_count += 1
        
        for issue in self.result.cross_exchange_sync_issues:
            if issue['severity'] == 'CRITICAL':
                critical_count += 1
            elif issue['severity'] == 'HIGH':
                high_count += 1
        
        if self.result.data_distribution_issues.get('severity') == 'CRITICAL':
            critical_count += 1
        elif self.result.data_distribution_issues.get('severity') == 'HIGH':
            high_count += 1
        
        # 确定整体严重性
        if critical_count > 0:
            self.result.severity = "CRITICAL"
        elif high_count > 0:
            self.result.severity = "HIGH"
        else:
            self.result.severity = "MEDIUM"
        
        # 生成针对性修复建议
        recommendations = []
        
        # OKX时间戳过期问题建议
        okx_issues = [issue for issue in self.result.timestamp_issues if issue['exchange'] == 'okx']
        if okx_issues:
            recommendations.append({
                'priority': 'CRITICAL',
                'issue': 'OKX时间戳过期数据大量丢弃',
                'root_cause': 'OKX WebSocket数据中ts字段不是实时时间戳，而是历史订单簿快照时间',
                'solution': '修改unified_timestamp_processor.py中的时间戳处理策略：\n'
                           '1. 对OKX交易所禁用服务器时间戳，强制使用统一时间基准\n'
                           '2. 或者调整max_age_ms阈值从2000ms增加到120000ms(2分钟)\n'
                           '3. 增加OKX专门的时间戳处理逻辑',
                'files_to_modify': [
                    'websocket/unified_timestamp_processor.py',
                    'websocket/okx_ws.py'
                ]
            })
        
        # 跨交易所同步问题建议
        if self.result.cross_exchange_sync_issues:
            recommendations.append({
                'priority': 'HIGH',
                'issue': '跨交易所时间戳不同步',
                'root_cause': 'Gate和OKX时间戳差异超过3秒，超出800ms阈值',
                'solution': '调整跨交易所同步容忍度：\n'
                           '1. 将max_diff_ms从800ms增加到5000ms\n'
                           '2. 或者强化交易所时间同步机制\n'
                           '3. 增加智能时间戳修正逻辑',
                'files_to_modify': [
                    'core/opportunity_scanner.py',
                    'websocket/unified_timestamp_processor.py'
                ]
            })
        
        # Gate静默断开问题建议
        if self.result.silent_disconnects:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Gate交易所静默断开',
                'root_cause': 'Gate WebSocket连接60-74秒静默断开，无数据更新',
                'solution': '增强Gate WebSocket连接管理：\n'
                           '1. 缩短心跳间隔从20秒到10秒\n'
                           '2. 增加连接质量检测和主动重连\n'
                           '3. 实施连接池管理和备用连接',
                'files_to_modify': [
                    'websocket/gate_ws.py',
                    'websocket/ws_manager.py',
                    'websocket/unified_connection_pool_manager.py'
                ]
            })
        
        # 数据分布不均衡问题建议
        if self.result.data_distribution_issues.get('severity') in ['CRITICAL', 'HIGH']:
            recommendations.append({
                'priority': 'HIGH',
                'issue': '数据流不均衡导致套利机会丢失',
                'root_cause': 'D组合数据正常，其他组合因时间戳问题大量数据被丢弃',
                'solution': '这是时间戳问题的连锁反应，解决上述时间戳问题后自动改善：\n'
                           '1. 修复OKX时间戳过期问题\n'
                           '2. 修复跨交易所同步问题\n'
                           '3. 修复Gate静默断开问题\n'
                           '4. 监控各组合数据流均衡性',
                'files_to_modify': [
                    'core/opportunity_scanner.py'
                ]
            })
        
        self.result.recommendations = recommendations

    async def generate_detailed_report(self) -> str:
        """生成详细诊断报告"""
        report_lines = []
        
        report_lines.append("=" * 80)
        report_lines.append("🔍 时间戳问题精确诊断报告")
        report_lines.append("=" * 80)
        report_lines.append(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"整体严重性: {self.result.severity}")
        report_lines.append("")
        
        # 1. 时间戳过期问题
        if self.result.timestamp_issues:
            report_lines.append("📊 1. 时间戳过期数据丢弃问题")
            report_lines.append("-" * 50)
            for issue in self.result.timestamp_issues:
                report_lines.append(f"🚨 {issue['exchange'].upper()}交易所:")
                report_lines.append(f"   - 过期数据次数: {issue['count']}")
                report_lines.append(f"   - 平均过期时间: {issue['avg_age_ms']/1000:.1f}秒")
                report_lines.append(f"   - 最大过期时间: {issue['max_age_ms']/1000:.1f}秒")
                report_lines.append(f"   - 严重性: {issue['severity']}")
                report_lines.append("")
        
        # 2. 静默断开问题
        if self.result.silent_disconnects:
            report_lines.append("📊 2. 静默断开问题")
            report_lines.append("-" * 50)
            for disconnect in self.result.silent_disconnects:
                report_lines.append(f"🔇 {disconnect['time']} - {disconnect['key']}:")
                report_lines.append(f"   - 静默时长: {disconnect['duration']:.1f}秒")
                report_lines.append(f"   - 严重性: {disconnect['severity']}")
            report_lines.append("")
        
        # 3. 跨交易所同步问题
        if self.result.cross_exchange_sync_issues:
            report_lines.append("📊 3. 跨交易所时间戳同步问题")
            report_lines.append("-" * 50)
            for issue in self.result.cross_exchange_sync_issues:
                report_lines.append(f"⏰ {issue['exchange_pair']}:")
                report_lines.append(f"   - 不同步次数: {issue['count']}")
                report_lines.append(f"   - 平均时间差: {issue['avg_diff_ms']:.1f}ms")
                report_lines.append(f"   - 最大时间差: {issue['max_diff_ms']:.1f}ms")
                report_lines.append(f"   - 严重性: {issue['severity']}")
            report_lines.append("")
        
        # 4. 数据分布问题
        if self.result.data_distribution_issues:
            issue = self.result.data_distribution_issues
            report_lines.append("📊 4. 数据分布不均衡问题")
            report_lines.append("-" * 50)
            report_lines.append(f"📈 总记录数: {issue['total_records']}")
            report_lines.append(f"🚨 不均衡比例: {issue['imbalance_ratio']:.1f}:1")
            report_lines.append(f"   各组合分布:")
            for combo, count in issue['combo_counts'].items():
                percentage = (count / issue['total_records']) * 100
                report_lines.append(f"   {combo}: {count:4d}条 ({percentage:5.1f}%)")
            report_lines.append(f"   严重性: {issue['severity']}")
            report_lines.append("")
        
        # 5. 修复建议
        if self.result.recommendations:
            report_lines.append("💡 5. 修复建议")
            report_lines.append("-" * 50)
            for i, rec in enumerate(self.result.recommendations, 1):
                report_lines.append(f"建议 {i}: {rec['issue']} [{rec['priority']}]")
                report_lines.append(f"根本原因: {rec['root_cause']}")
                report_lines.append(f"解决方案:")
                for line in rec['solution'].split('\n'):
                    report_lines.append(f"  {line}")
                report_lines.append(f"需要修改的文件: {', '.join(rec['files_to_modify'])}")
                report_lines.append("")
        
        report_lines.append("=" * 80)
        report_lines.append("🎯 诊断结论: 这不是网络问题，而是代码中时间戳处理逻辑的问题")
        report_lines.append("主要原因: OKX WebSocket数据中的时间戳字段不是实时数据，导致系统误判为过期")  
        report_lines.append("建议优先级: 修复OKX时间戳处理 > 调整同步容忍度 > 增强连接管理")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)

async def main():
    """主函数"""
    print("🔍 启动时间戳问题精确诊断...")
    
    diagnosis = TimestampIssuesDiagnosis()
    result = await diagnosis.diagnose_all_issues()
    
    # 生成详细报告
    report = await diagnosis.generate_detailed_report()
    print(report)
    
    # 保存报告到文件
    report_file = os.path.join(BASE_DIR, "diagnostic_results", "timestamp_issues_diagnosis_report.txt")
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 详细报告已保存到: {report_file}")
    
    # 返回结果用于进一步处理
    return result

if __name__ == "__main__":
    asyncio.run(main())