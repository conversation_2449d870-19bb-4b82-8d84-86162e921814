#!/usr/bin/env python3
"""
🔥 时间戳修复验证脚本

验证修复后的时间戳处理是否正确：
1. 确保所有时间戳都是整数
2. 确保时间戳单位统一为毫秒
3. 验证跨交易所同步检查
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append('/root/myproject/123/66B 修复时间戳问题，但是全是错误/123')

def test_timestamp_integer_consistency():
    """测试时间戳整数一致性"""
    print("🔍 时间戳整数一致性测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp, TimestampProcessor
        
        # 创建测试处理器
        processor = TimestampProcessor("okx")
        
        # 测试各种输入格式
        test_cases = [
            ("整数毫秒", 1754055467805),
            ("浮点毫秒", 1754055467805.0),
            ("整数秒", 1754055467),
            ("浮点秒", 1754055467.805),
            ("字符串毫秒", "1754055467805"),
            ("字符串秒", "1754055467.805"),
            ("纳秒", 1754055467805000000),
        ]
        
        print("📊 ensure_milliseconds_timestamp 测试:")
        for desc, input_ts in test_cases:
            try:
                result = ensure_milliseconds_timestamp(input_ts)
                is_int = isinstance(result, int)
                print(f"{desc:12} | 输入: {str(input_ts):>20} | 输出: {result:>15} | 整数: {'✅' if is_int else '❌'}")
                
                if not is_int:
                    print(f"             | ❌ 错误：输出不是整数，类型为 {type(result)}")
                    
            except Exception as e:
                print(f"{desc:12} | 输入: {str(input_ts):>20} | 错误: {e}")
        
        # 测试 _normalize_timestamp_format
        print(f"\n📊 _normalize_timestamp_format 测试:")
        for desc, input_ts in test_cases:
            if isinstance(input_ts, (int, float)):
                try:
                    result = processor._normalize_timestamp_format(float(input_ts))
                    is_int = isinstance(result, int)
                    print(f"{desc:12} | 输入: {str(input_ts):>20} | 输出: {result:>15} | 整数: {'✅' if is_int else '❌'}")
                    
                    if not is_int:
                        print(f"             | ❌ 错误：输出不是整数，类型为 {type(result)}")
                        
                except Exception as e:
                    print(f"{desc:12} | 输入: {str(input_ts):>20} | 错误: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入模块: {e}")
        return False

def test_cross_exchange_sync():
    """测试跨交易所同步检查"""
    print("\n🔍 跨交易所同步检查测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        processor = get_timestamp_processor("gate")
        
        # 测试用例：模拟日志中的实际数据
        test_cases = [
            {
                "desc": "正常同步",
                "ts1": 1754055479586,
                "ts2": 1754055479600,
                "expected_diff": 14,
                "should_sync": True
            },
            {
                "desc": "轻微不同步",
                "ts1": 1754055479586,
                "ts2": 1754055480386,  # 800ms差异
                "expected_diff": 800,
                "should_sync": True  # 刚好在阈值边界
            },
            {
                "desc": "严重不同步",
                "ts1": 1754055479586,
                "ts2": 1754055489526,  # 9940ms差异
                "expected_diff": 9940,
                "should_sync": False
            },
            {
                "desc": "浮点数输入",
                "ts1": 1754055479586.0,  # 浮点数
                "ts2": 1754055479600.0,  # 浮点数
                "expected_diff": 14,
                "should_sync": True
            }
        ]
        
        for case in test_cases:
            try:
                is_synced, time_diff_ms = processor.validate_cross_exchange_sync(
                    case["ts1"], case["ts2"], "gate", "bybit", max_diff_ms=800
                )
                
                diff_correct = abs(time_diff_ms - case["expected_diff"]) < 1
                sync_correct = is_synced == case["should_sync"]
                is_int_diff = isinstance(time_diff_ms, (int, float)) and not isinstance(time_diff_ms, bool)
                
                status = "✅" if (diff_correct and sync_correct and is_int_diff) else "❌"
                
                print(f"{case['desc']:12} | 时间差: {time_diff_ms:>8.1f}ms | 同步: {is_synced:>5} | {status}")
                
                if not diff_correct:
                    print(f"             | ❌ 时间差计算错误：期望 {case['expected_diff']}, 实际 {time_diff_ms}")
                if not sync_correct:
                    print(f"             | ❌ 同步判断错误：期望 {case['should_sync']}, 实际 {is_synced}")
                if not is_int_diff:
                    print(f"             | ❌ 时间差类型错误：{type(time_diff_ms)}")
                    
            except Exception as e:
                print(f"{case['desc']:12} | 错误: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入模块: {e}")
        return False

def test_data_age_calculation():
    """测试数据年龄计算"""
    print("\n🔍 数据年龄计算测试")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import calculate_data_age
        
        current_time = time.time()
        current_time_ms = int(current_time * 1000)
        
        test_cases = [
            {
                "desc": "1秒前数据",
                "data_ts": current_time_ms - 1000,
                "current_time": current_time,
                "expected_age": 1.0
            },
            {
                "desc": "500ms前数据",
                "data_ts": current_time_ms - 500,
                "current_time": current_time,
                "expected_age": 0.5
            },
            {
                "desc": "浮点数时间戳",
                "data_ts": float(current_time_ms - 1000),
                "current_time": current_time,
                "expected_age": 1.0
            },
            {
                "desc": "秒级时间戳",
                "data_ts": int(current_time - 1),
                "current_time": current_time,
                "expected_age": 1.0
            }
        ]
        
        for case in test_cases:
            try:
                age = calculate_data_age(case["data_ts"], case["current_time"])
                age_correct = abs(age - case["expected_age"]) < 0.1  # 100ms容忍度
                is_float = isinstance(age, float)
                
                status = "✅" if (age_correct and is_float) else "❌"
                
                print(f"{case['desc']:15} | 年龄: {age:>6.3f}s | 期望: {case['expected_age']:>6.3f}s | {status}")
                
                if not age_correct:
                    print(f"                | ❌ 年龄计算错误：期望 {case['expected_age']}, 实际 {age}")
                if not is_float:
                    print(f"                | ❌ 年龄类型错误：{type(age)}")
                    
            except Exception as e:
                print(f"{case['desc']:15} | 错误: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入模块: {e}")
        return False

def main():
    """主函数"""
    print("🔥 时间戳修复验证")
    print("=" * 80)
    print(f"验证时间: {datetime.now()}")
    print()
    
    results = []
    
    # 测试时间戳整数一致性
    results.append(test_timestamp_integer_consistency())
    
    # 测试跨交易所同步检查
    results.append(test_cross_exchange_sync())
    
    # 测试数据年龄计算
    results.append(test_data_age_calculation())
    
    # 总结
    print("\n🎯 验证结果:")
    print("=" * 60)
    passed_tests = sum(results)
    total_tests = len(results)
    
    if passed_tests == total_tests:
        print(f"✅ 所有测试通过 ({passed_tests}/{total_tests})")
        print("✅ 时间戳修复成功！")
    else:
        print(f"❌ 部分测试失败 ({passed_tests}/{total_tests})")
        print("❌ 需要进一步修复")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
