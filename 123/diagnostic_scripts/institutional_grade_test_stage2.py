#!/usr/bin/env python3
"""
机构级别测试 - 第二段：复杂系统级联测试
涉及模块之间的交互逻辑、状态联动、多币种切换、多交易所分支
验证系统协同一致性
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any, List, Optional
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstitutionalGradeTestStage2:
    """机构级别测试 - 第二段：复杂系统级联测试"""
    
    def __init__(self):
        self.test_results = {
            "stage": "复杂系统级联测试",
            "timestamp": int(time.time()),
            "tests": {},
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "success_rate": 0.0
            }
        }
    
    async def test_multi_exchange_coordination(self):
        """测试多交易所协调一致性"""
        print("🔍 测试多交易所协调一致性...")
        
        test_name = "multi_exchange_coordination"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor, check_all_timestamp_sync_health
            from core.opportunity_scanner import OpportunityScanner
            
            # 测试所有交易所健康状态
            print("  📋 测试交易所健康状态检查")
            
            health_status = await check_all_timestamp_sync_health()
            
            for exchange, status in health_status.items():
                assert 'is_healthy' in status, f"{exchange} 缺少健康状态"
                assert 'health_level' in status, f"{exchange} 缺少健康等级"
                
                test_cases.append({
                    "case": f"{exchange}_health_check",
                    "status": "PASS" if status['is_healthy'] else "FAIL",
                    "health_level": status['health_level'],
                    "health_status": status
                })
                
                if status['is_healthy']:
                    print(f"    ✅ {exchange.upper()}: 健康状态良好 ({status['health_level']})")
                else:
                    print(f"    ⚠️ {exchange.upper()}: 健康状态异常 ({status['health_level']})")
            
            # 测试跨交易所时间戳一致性
            print("  📋 测试跨交易所时间戳一致性")
            
            exchanges = ["gate", "bybit", "okx"]
            processors = {}
            timestamps = {}
            
            # 获取所有交易所的时间戳
            for exchange in exchanges:
                processors[exchange] = get_timestamp_processor(exchange)
                timestamps[exchange] = processors[exchange].get_synced_timestamp()
            
            # 验证所有交易所对之间的时间同步
            sync_matrix = {}
            for i, exchange1 in enumerate(exchanges):
                for exchange2 in exchanges[i+1:]:
                    is_synced, time_diff = processors[exchange1].validate_cross_exchange_sync(
                        timestamps[exchange1], timestamps[exchange2],
                        exchange1, exchange2, max_diff_ms=800
                    )
                    
                    sync_key = f"{exchange1}_{exchange2}"
                    sync_matrix[sync_key] = {
                        "is_synced": is_synced,
                        "time_diff_ms": time_diff
                    }
                    
                    test_cases.append({
                        "case": f"cross_sync_{sync_key}",
                        "status": "PASS" if is_synced else "FAIL",
                        "time_diff_ms": time_diff,
                        "threshold_ms": 800
                    })
                    
                    if is_synced:
                        print(f"    ✅ {exchange1.upper()} ↔ {exchange2.upper()}: 同步正常 ({time_diff:.1f}ms)")
                    else:
                        print(f"    ❌ {exchange1.upper()} ↔ {exchange2.upper()}: 同步失败 ({time_diff:.1f}ms)")
                        raise AssertionError(f"跨交易所同步失败: {exchange1} vs {exchange2}")
            
            # 测试套利机会扫描器的时间戳验证
            print("  📋 测试套利机会扫描器时间戳验证")
            
            try:
                scanner = OpportunityScanner()
                
                # 模拟价格数据进行时间戳验证测试
                mock_spot_data = {
                    "symbol": "BTC-USDT",
                    "exchange": "gate",
                    "price": 50000.0,
                    "timestamp": timestamps["gate"]
                }
                
                mock_futures_data = {
                    "symbol": "BTC-USDT",
                    "exchange": "bybit", 
                    "price": 50100.0,
                    "timestamp": timestamps["bybit"]
                }
                
                # 验证时间戳同步检查逻辑
                time_diff_ms = abs(mock_spot_data["timestamp"] - mock_futures_data["timestamp"])
                is_time_synced = time_diff_ms <= 800  # OpportunityScanner的阈值
                
                test_cases.append({
                    "case": "opportunity_scanner_timestamp_validation",
                    "status": "PASS" if is_time_synced else "FAIL",
                    "spot_timestamp": mock_spot_data["timestamp"],
                    "futures_timestamp": mock_futures_data["timestamp"],
                    "time_diff_ms": time_diff_ms,
                    "is_synced": is_time_synced
                })
                
                if is_time_synced:
                    print(f"    ✅ 套利扫描器: 时间戳验证正常 ({time_diff_ms:.1f}ms)")
                else:
                    print(f"    ❌ 套利扫描器: 时间戳验证失败 ({time_diff_ms:.1f}ms)")
                    raise AssertionError("套利扫描器时间戳验证失败")
                
            except ImportError:
                print("    ⚠️ 套利扫描器模块不可用，跳过相关测试")
                test_cases.append({
                    "case": "opportunity_scanner_timestamp_validation",
                    "status": "SKIP",
                    "reason": "OpportunityScanner module not available"
                })
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases,
                "sync_matrix": sync_matrix,
                "health_status": health_status
            }
            
            passed_cases = len([c for c in test_cases if c["status"] == "PASS"])
            total_cases = len([c for c in test_cases if c["status"] != "SKIP"])
            
            print(f"✅ 多交易所协调一致性测试通过 ({passed_cases}/{total_cases} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 多交易所协调一致性测试失败: {e}")
            return False
    
    async def test_websocket_manager_lifecycle(self):
        """测试WebSocket管理器生命周期"""
        print("🔍 测试WebSocket管理器生命周期...")
        
        test_name = "websocket_manager_lifecycle"
        test_cases = []
        
        try:
            from websocket.ws_manager import WebSocketManager
            
            # 创建WebSocket管理器实例
            ws_manager = WebSocketManager()
            
            # 测试初始化状态
            print("  📋 测试初始化状态")
            
            assert not ws_manager.running, "管理器初始状态应为未运行"
            assert not ws_manager.initialized, "管理器初始状态应为未初始化"
            
            test_cases.append({
                "case": "initial_state",
                "status": "PASS",
                "running": ws_manager.running,
                "initialized": ws_manager.initialized
            })
            
            print("    ✅ 初始状态正常")
            
            # 测试集中式时间同步
            print("  📋 测试集中式时间同步集成")
            
            # 验证集中式同步方法
            assert hasattr(ws_manager, '_centralized_time_sync'), "缺少集中式同步方法"
            
            # 执行集中式同步
            await ws_manager._centralized_time_sync()
            
            test_cases.append({
                "case": "centralized_sync_integration",
                "status": "PASS",
                "method_available": True
            })
            
            print("    ✅ 集中式时间同步集成正常")
            
            # 测试状态管理
            print("  📋 测试状态管理")
            
            # 验证状态属性
            assert hasattr(ws_manager, 'clients'), "缺少客户端字典"
            assert hasattr(ws_manager, 'client_tasks'), "缺少客户端任务列表"
            
            test_cases.append({
                "case": "state_management",
                "status": "PASS",
                "has_clients": hasattr(ws_manager, 'clients'),
                "has_tasks": hasattr(ws_manager, 'client_tasks')
            })
            
            print("    ✅ 状态管理正常")
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases
            }
            
            print(f"✅ WebSocket管理器生命周期测试通过 ({len(test_cases)} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ WebSocket管理器生命周期测试失败: {e}")
            return False
    
    async def test_data_flow_consistency(self):
        """测试数据流一致性"""
        print("🔍 测试数据流一致性...")
        
        test_name = "data_flow_consistency"
        test_cases = []
        
        try:
            from websocket.unified_timestamp_processor import get_synced_timestamp
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            # 测试统一时间戳接口
            print("  📋 测试统一时间戳接口")
            
            exchanges = ["gate", "bybit", "okx"]
            
            for exchange in exchanges:
                # 测试全局时间戳接口
                timestamp1 = get_synced_timestamp(exchange)
                timestamp2 = get_synced_timestamp(exchange, {})
                timestamp3 = get_synced_timestamp(exchange, {"test": "data"})
                
                # 验证时间戳格式一致性
                for i, ts in enumerate([timestamp1, timestamp2, timestamp3], 1):
                    assert isinstance(ts, int), f"{exchange} 时间戳{i}必须是整数"
                    assert ts > 1700000000000, f"{exchange} 时间戳{i}必须是毫秒级"
                    
                    # 验证时间戳合理性（不能相差太大）
                    current_time = int(time.time() * 1000)
                    time_diff = abs(ts - current_time)
                    assert time_diff < 10000, f"{exchange} 时间戳{i}与当前时间差异过大"
                
                test_cases.append({
                    "case": f"{exchange}_unified_timestamp_interface",
                    "status": "PASS",
                    "timestamps": [timestamp1, timestamp2, timestamp3]
                })
                
                print(f"    ✅ {exchange.upper()}: 统一时间戳接口正常")
            
            # 测试数据格式化器
            print("  📋 测试统一数据格式化器")
            
            try:
                formatter = get_orderbook_formatter()
                
                # 测试订单簿格式化
                mock_asks = [[50100.0, 1.0], [50200.0, 2.0]]
                mock_bids = [[50000.0, 1.5], [49900.0, 2.5]]
                
                for exchange in exchanges:
                    timestamp = get_synced_timestamp(exchange)
                    
                    orderbook_data = formatter.format_orderbook_data(
                        asks=mock_asks,
                        bids=mock_bids,
                        symbol="BTC-USDT",
                        exchange=exchange,
                        market_type="spot",
                        timestamp=timestamp
                    )
                    
                    # 验证格式化结果
                    assert isinstance(orderbook_data, dict), f"{exchange} 订单簿数据必须是字典"
                    assert 'asks' in orderbook_data, f"{exchange} 缺少asks数据"
                    assert 'bids' in orderbook_data, f"{exchange} 缺少bids数据"
                    assert 'timestamp' in orderbook_data, f"{exchange} 缺少时间戳"
                    assert 'exchange' in orderbook_data, f"{exchange} 缺少交易所标识"
                    
                    test_cases.append({
                        "case": f"{exchange}_orderbook_formatting",
                        "status": "PASS",
                        "formatted_data_keys": list(orderbook_data.keys())
                    })
                    
                    print(f"    ✅ {exchange.upper()}: 订单簿格式化正常")
                
            except ImportError:
                print("    ⚠️ 统一数据格式化器不可用，跳过相关测试")
                for exchange in exchanges:
                    test_cases.append({
                        "case": f"{exchange}_orderbook_formatting",
                        "status": "SKIP",
                        "reason": "Unified data formatter not available"
                    })
            
            self.test_results["tests"][test_name] = {
                "status": "PASS",
                "test_cases": test_cases
            }
            
            passed_cases = len([c for c in test_cases if c["status"] == "PASS"])
            total_cases = len([c for c in test_cases if c["status"] != "SKIP"])
            
            print(f"✅ 数据流一致性测试通过 ({passed_cases}/{total_cases} 个测试用例)")
            return True
            
        except Exception as e:
            self.test_results["tests"][test_name] = {
                "status": "FAIL",
                "error": str(e),
                "test_cases": test_cases
            }
            print(f"❌ 数据流一致性测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有复杂系统级联测试"""
        print("🚀 启动机构级别测试 - 第二段：复杂系统级联测试")
        print("=" * 80)
        
        tests = [
            self.test_multi_exchange_coordination,
            self.test_websocket_manager_lifecycle,
            self.test_data_flow_consistency
        ]
        
        passed_tests = 0
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed_tests += 1
                print()  # 空行分隔
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
                print()
        
        # 更新总结
        self.test_results["summary"]["total_tests"] = len(tests)
        self.test_results["summary"]["passed_tests"] = passed_tests
        self.test_results["summary"]["failed_tests"] = len(tests) - passed_tests
        self.test_results["summary"]["success_rate"] = (passed_tests / len(tests)) * 100
        
        # 保存测试结果
        output_file = "diagnostic_scripts/institutional_test_stage2_result.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
            print(f"✅ 测试结果已保存到: {output_file}")
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")
        
        # 生成测试报告
        print("=" * 80)
        print("📊 复杂系统级联测试报告")
        print("=" * 80)
        print(f"总测试数: {self.test_results['summary']['total_tests']}")
        print(f"通过测试: {self.test_results['summary']['passed_tests']}")
        print(f"失败测试: {self.test_results['summary']['failed_tests']}")
        print(f"成功率: {self.test_results['summary']['success_rate']:.1f}%")
        
        if self.test_results['summary']['success_rate'] == 100.0:
            print("🎉 复杂系统级联测试100%通过 - 系统协同一致性完美")
            return True
        else:
            print("❌ 复杂系统级联测试未完全通过 - 需要进一步修复")
            return False

async def main():
    """主函数"""
    tester = InstitutionalGradeTestStage2()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 第二段测试结论: 系统协同一致性100%正常，可以进行第三段测试")
        return 0
    else:
        print("\n❌ 第二段测试失败: 系统协同存在问题，必须先修复")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
