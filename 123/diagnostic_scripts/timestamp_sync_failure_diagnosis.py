#!/usr/bin/env python3
"""
时间戳同步失败精确诊断脚本
模拟失败场景，精准定位时间戳同步错误的根本原因
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_timestamp_processor_initialization():
    """测试时间戳处理器初始化和同步"""
    print("🔍 测试时间戳处理器初始化和同步...")
    
    results = {}
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor, initialize_all_timestamp_processors
        
        # 测试各个交易所的时间戳处理器
        exchanges = ["gate", "bybit", "okx"]
        
        for exchange in exchanges:
            print(f"\n📋 测试 {exchange.upper()} 时间戳处理器:")
            
            try:
                # 获取处理器实例
                processor = get_timestamp_processor(exchange)
                print(f"  ✅ 处理器实例创建成功")
                
                # 检查初始状态
                initial_status = processor.get_sync_status()
                print(f"  📊 初始状态: time_synced={initial_status['time_synced']}, offset={initial_status['time_offset_ms']}ms")
                
                # 尝试强制同步
                print(f"  🔄 尝试强制时间同步...")
                sync_start = time.time()
                sync_success = await processor.sync_time(force=True)
                sync_duration = time.time() - sync_start
                
                if sync_success:
                    print(f"  ✅ 时间同步成功 (耗时: {sync_duration:.2f}s)")
                    final_status = processor.get_sync_status()
                    print(f"  📊 同步后状态: time_synced={final_status['time_synced']}, offset={final_status['time_offset_ms']}ms")
                else:
                    print(f"  ❌ 时间同步失败 (耗时: {sync_duration:.2f}s)")
                    final_status = processor.get_sync_status()
                    print(f"  📊 失败后状态: time_synced={final_status['time_synced']}, retry_count={final_status['sync_retry_count']}")
                
                # 测试时间戳生成
                print(f"  🕐 测试时间戳生成...")
                timestamp = processor.get_synced_timestamp()
                current_time = int(time.time() * 1000)
                time_diff = abs(timestamp - current_time)
                print(f"  📊 生成时间戳: {timestamp}, 与当前时间差: {time_diff}ms")
                
                results[exchange] = {
                    "processor_created": True,
                    "initial_synced": initial_status['time_synced'],
                    "sync_success": sync_success,
                    "sync_duration": sync_duration,
                    "final_synced": final_status['time_synced'],
                    "final_offset": final_status['time_offset_ms'],
                    "retry_count": final_status.get('sync_retry_count', 0),
                    "timestamp_diff": time_diff
                }
                
            except Exception as e:
                print(f"  ❌ {exchange} 测试失败: {e}")
                results[exchange] = {
                    "processor_created": False,
                    "error": str(e)
                }
        
        return results
        
    except Exception as e:
        print(f"❌ 时间戳处理器测试失败: {e}")
        return {"error": str(e)}

async def test_server_time_api():
    """测试各交易所的服务器时间API"""
    print("\n🌐 测试各交易所服务器时间API...")
    
    api_urls = {
        "gate": "https://api.gateio.ws/api/v4/spot/time",
        "bybit": "https://api.bybit.com/v5/market/time", 
        "okx": "https://www.okx.com/api/v5/public/time"
    }
    
    results = {}
    
    try:
        import aiohttp
        import ssl
        
        # 创建SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=10,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        async with aiohttp.ClientSession(connector=connector) as session:
            for exchange, url in api_urls.items():
                print(f"\n📋 测试 {exchange.upper()} API: {url}")
                
                try:
                    start_time = time.time()
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        request_duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            print(f"  ✅ API响应成功 (耗时: {request_duration:.2f}s)")
                            print(f"  📊 响应数据: {data}")
                            
                            results[exchange] = {
                                "api_success": True,
                                "status_code": response.status,
                                "request_duration": request_duration,
                                "response_data": data
                            }
                        else:
                            print(f"  ❌ API响应错误: {response.status}")
                            results[exchange] = {
                                "api_success": False,
                                "status_code": response.status,
                                "request_duration": request_duration
                            }
                            
                except Exception as e:
                    print(f"  ❌ API请求失败: {e}")
                    results[exchange] = {
                        "api_success": False,
                        "error": str(e)
                    }
        
        return results
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return {"error": str(e)}

async def test_websocket_initialization():
    """测试WebSocket客户端初始化时的时间同步"""
    print("\n🔌 测试WebSocket客户端初始化时的时间同步...")
    
    try:
        # 模拟WebSocket客户端的时间同步调用
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        results = {}
        exchanges = ["gate", "bybit", "okx"]
        
        for exchange in exchanges:
            print(f"\n📋 模拟 {exchange.upper()} WebSocket初始化:")
            
            try:
                # 模拟WebSocket客户端的run方法中的时间同步调用
                processor = get_timestamp_processor(exchange)
                
                print(f"  🔄 执行强制时间同步 (模拟WebSocket启动)...")
                sync_start = time.time()
                sync_success = await processor.sync_time(force=True)
                sync_duration = time.time() - sync_start
                
                if sync_success:
                    print(f"  ✅ {exchange.upper()}时间同步成功，偏移量: {processor.time_offset}ms")
                    results[exchange] = {
                        "websocket_sync_success": True,
                        "sync_duration": sync_duration,
                        "time_offset": processor.time_offset,
                        "time_synced": processor.time_synced
                    }
                else:
                    print(f"  ⚠️ {exchange.upper()}时间同步失败，将使用本地时间（可能导致时间戳不一致）")
                    results[exchange] = {
                        "websocket_sync_success": False,
                        "sync_duration": sync_duration,
                        "time_synced": processor.time_synced,
                        "retry_count": processor.sync_retry_count
                    }
                    
            except Exception as e:
                print(f"  ❌ {exchange} WebSocket初始化模拟失败: {e}")
                results[exchange] = {
                    "websocket_sync_success": False,
                    "error": str(e)
                }
        
        return results
        
    except Exception as e:
        print(f"❌ WebSocket初始化测试失败: {e}")
        return {"error": str(e)}

async def analyze_sync_failure_impact():
    """分析时间同步失败对系统的影响"""
    print("\n📊 分析时间同步失败对系统的影响...")
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        # 获取所有处理器的状态
        exchanges = ["gate", "bybit", "okx"]
        processors = {}
        
        for exchange in exchanges:
            processors[exchange] = get_timestamp_processor(exchange)
        
        # 模拟跨交易所时间戳验证
        print("\n🔗 模拟跨交易所时间戳验证:")
        
        validation_results = []
        
        for i, exchange1 in enumerate(exchanges):
            for exchange2 in exchanges[i+1:]:
                processor1 = processors[exchange1]
                processor2 = processors[exchange2]
                
                # 生成时间戳
                timestamp1 = processor1.get_synced_timestamp()
                timestamp2 = processor2.get_synced_timestamp()
                
                # 验证跨交易所同步
                is_synced, time_diff_ms = processor1.validate_cross_exchange_sync(
                    timestamp1, timestamp2, exchange1, exchange2, max_diff_ms=800
                )
                
                print(f"  📊 {exchange1.upper()} vs {exchange2.upper()}: 同步={is_synced}, 时间差={time_diff_ms:.1f}ms")
                
                validation_results.append({
                    "exchange1": exchange1,
                    "exchange2": exchange2,
                    "timestamp1": timestamp1,
                    "timestamp2": timestamp2,
                    "is_synced": is_synced,
                    "time_diff_ms": time_diff_ms,
                    "processor1_synced": processor1.time_synced,
                    "processor2_synced": processor2.time_synced
                })
        
        return validation_results
        
    except Exception as e:
        print(f"❌ 同步失败影响分析失败: {e}")
        return {"error": str(e)}

async def main():
    """主函数"""
    print("🚀 启动时间戳同步失败精确诊断...")
    print("=" * 80)
    
    # 收集所有诊断结果
    diagnosis_results = {
        "timestamp": int(time.time()),
        "diagnosis_type": "timestamp_sync_failure",
        "tests": {}
    }
    
    # 1. 测试时间戳处理器初始化
    processor_results = await test_timestamp_processor_initialization()
    diagnosis_results["tests"]["processor_initialization"] = processor_results
    
    # 2. 测试服务器时间API
    api_results = await test_server_time_api()
    diagnosis_results["tests"]["server_time_api"] = api_results
    
    # 3. 测试WebSocket初始化
    websocket_results = await test_websocket_initialization()
    diagnosis_results["tests"]["websocket_initialization"] = websocket_results
    
    # 4. 分析同步失败影响
    impact_results = await analyze_sync_failure_impact()
    diagnosis_results["tests"]["sync_failure_impact"] = impact_results
    
    # 保存诊断结果
    output_file = "diagnostic_scripts/timestamp_sync_failure_diagnosis_result.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(diagnosis_results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n✅ 诊断结果已保存到: {output_file}")
    except Exception as e:
        print(f"\n⚠️ 保存诊断结果失败: {e}")
    
    # 生成诊断总结
    print("\n" + "=" * 80)
    print("🎯 诊断总结:")
    print("=" * 80)
    
    # 分析处理器初始化结果
    if "processor_initialization" in diagnosis_results["tests"]:
        proc_results = diagnosis_results["tests"]["processor_initialization"]
        if isinstance(proc_results, dict) and "error" not in proc_results:
            failed_syncs = [ex for ex, result in proc_results.items() 
                          if isinstance(result, dict) and not result.get("sync_success", False)]
            if failed_syncs:
                print(f"🚨 时间同步失败的交易所: {failed_syncs}")
                print("   - 这解释了为什么日志中所有交易所都显示 'not_synced'")
            else:
                print("✅ 所有交易所时间同步成功")
    
    # 分析API测试结果
    if "server_time_api" in diagnosis_results["tests"]:
        api_results = diagnosis_results["tests"]["server_time_api"]
        if isinstance(api_results, dict) and "error" not in api_results:
            failed_apis = [ex for ex, result in api_results.items() 
                         if isinstance(result, dict) and not result.get("api_success", False)]
            if failed_apis:
                print(f"🌐 API访问失败的交易所: {failed_apis}")
                print("   - 这是时间同步失败的直接原因")
    
    print("\n🔧 建议修复措施:")
    print("1. 检查网络连接和防火墙设置")
    print("2. 验证交易所API的可访问性")
    print("3. 增强时间同步的重试和容错机制")
    print("4. 考虑使用备用时间同步方案")

if __name__ == "__main__":
    asyncio.run(main())
