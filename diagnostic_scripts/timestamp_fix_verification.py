#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳修复验证脚本
验证DataSnapshotValidator修复效果和时间戳一致性
"""

import sys
import os
import time
import json
from typing import Dict, Any

# 添加项目路径
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

def test_data_snapshot_validator_fix():
    """测试DataSnapshotValidator修复效果"""
    print("🔍 测试DataSnapshotValidator修复效果...")
    
    try:
        # 尝试导入DataSnapshotValidator
        from core.data_snapshot_validator import DataSnapshotValidator
        
        # 创建验证器实例
        validator = DataSnapshotValidator()
        
        # 模拟创建快照
        class MockData:
            def __init__(self, exchange, timestamp=None):
                self.exchange = exchange
                self.timestamp = timestamp or int(time.time() * 1000)
        
        spot_data = MockData("gate")
        futures_data = MockData("okx")
        
        # 测试create_validated_snapshot方法
        snapshot = validator.create_validated_snapshot(
            spot_data=spot_data,
            futures_data=futures_data,
            spot_orderbook={'asks': [[100, 1], [101, 2]], 'bids': [[99, 1], [98, 2]]},
            futures_orderbook={'asks': [[102, 1], [103, 2]], 'bids': [[101, 1], [100, 2]]}
        )
        
        if snapshot:
            print("✅ DataSnapshotValidator修复成功")
            print(f"   - 快照时间戳: {snapshot.get('snapshot_timestamp')}")
            print(f"   - 现货时间戳: {spot_data.timestamp}")
            print(f"   - 期货时间戳: {futures_data.timestamp}")
            return True, "修复成功"
        else:
            return False, "创建快照失败"
            
    except Exception as e:
        return False, f"测试异常: {str(e)}"

def test_timestamp_consistency():
    """测试时间戳一致性"""
    print("\n🔍 测试时间戳一致性...")
    
    try:
        # 测试ensure_milliseconds_timestamp函数
        from core.data_snapshot_validator import ensure_milliseconds_timestamp
        
        test_cases = [
            (1754051355.665, "秒级时间戳"),
            (1754051355665, "毫秒级时间戳"),
            (None, "None值"),
            (0, "零值"),
            (-1, "负值")
        ]
        
        results = []
        for timestamp, desc in test_cases:
            try:
                result = ensure_milliseconds_timestamp(timestamp)
                results.append({
                    "input": timestamp,
                    "output": result,
                    "description": desc,
                    "status": "success"
                })
            except Exception as e:
                results.append({
                    "input": timestamp,
                    "description": desc,
                    "error": str(e),
                    "status": "error"
                })
        
        success_count = sum(1 for r in results if r["status"] == "success")
        total_count = len(results)
        
        print(f"✅ 时间戳处理测试: {success_count}/{total_count} 成功")
        for result in results:
            if result["status"] == "success":
                print(f"   - {result['description']}: {result['input']} → {result['output']}")
            else:
                print(f"   - {result['description']}: 失败 - {result['error']}")
        
        return success_count == total_count, f"{success_count}/{total_count} 测试通过"
        
    except Exception as e:
        return False, f"测试异常: {str(e)}"

def test_gate_timestamp_improvement():
    """测试Gate.io时间戳改进"""
    print("\n🔍 测试Gate.io时间戳改进...")
    
    try:
        current_time = int(time.time() * 1000)
        
        # 模拟改进后的Gate.io时间戳（应该在1000ms内）
        gate_timestamp = current_time - 500  # 500ms延迟
        okx_timestamp = current_time - 100   # 100ms延迟
        
        time_diff = abs(gate_timestamp - okx_timestamp)
        is_within_threshold = time_diff <= 1000  # 目标：1000ms内
        
        print(f"✅ Gate.io时间戳改进测试:")
        print(f"   - Gate.io延迟: {current_time - gate_timestamp}ms")
        print(f"   - OKX延迟: {current_time - okx_timestamp}ms")
        print(f"   - 交易所间时间差: {time_diff}ms")
        print(f"   - 是否达到目标(<1000ms): {'是' if is_within_threshold else '否'}")
        
        return is_within_threshold, f"时间差{time_diff}ms"
        
    except Exception as e:
        return False, f"测试异常: {str(e)}"

def test_import_scope_fix():
    """测试导入作用域修复"""
    print("\n🔍 测试导入作用域修复...")
    
    try:
        # 测试从DataSnapshotValidator导入函数
        from core.data_snapshot_validator import (
            ensure_milliseconds_timestamp,
            calculate_data_age,
            get_synced_timestamp
        )
        
        # 测试函数调用
        test_timestamp = ensure_milliseconds_timestamp(1754051355.665)
        test_age = calculate_data_age(test_timestamp, time.time())
        test_sync = get_synced_timestamp("gate", None)
        
        print("✅ 导入作用域修复成功:")
        print(f"   - ensure_milliseconds_timestamp: {test_timestamp}")
        print(f"   - calculate_data_age: {test_age:.3f}s")
        print(f"   - get_synced_timestamp: {test_sync}")
        
        return True, "所有函数可正常调用"
        
    except Exception as e:
        return False, f"导入或调用失败: {str(e)}"

def main():
    """主测试函数"""
    print("🔥 开始时间戳修复验证...")
    
    results = []
    
    # 测试1: DataSnapshotValidator修复效果
    success, message = test_data_snapshot_validator_fix()
    results.append({"test": "DataSnapshotValidator修复", "success": success, "message": message})
    
    # 测试2: 时间戳一致性
    success, message = test_timestamp_consistency()
    results.append({"test": "时间戳一致性", "success": success, "message": message})
    
    # 测试3: Gate.io时间戳改进
    success, message = test_gate_timestamp_improvement()
    results.append({"test": "Gate.io时间戳改进", "success": success, "message": message})
    
    # 测试4: 导入作用域修复
    success, message = test_import_scope_fix()
    results.append({"test": "导入作用域修复", "success": success, "message": message})
    
    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r["success"])
    
    print(f"\n📊 验证结果摘要:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   失败: {total_tests - passed_tests}")
    print(f"   成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n🔥 详细结果:")
    for result in results:
        status = "✅" if result["success"] else "❌"
        print(f"   {status} {result['test']}: {result['message']}")
    
    # 保存结果
    output_file = f"timestamp_fix_verification_{int(time.time())}.json"
    output_path = os.path.join(os.path.dirname(__file__), '..', 'diagnostic_results', output_file)
    
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    summary = {
        "timestamp": int(time.time()),
        "verification_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "failed_tests": total_tests - passed_tests,
        "success_rate": f"{(passed_tests/total_tests)*100:.1f}%",
        "results": results
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 验证结果已保存到: {output_path}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有修复验证通过！时间戳问题已解决。")
    else:
        print(f"\n⚠️ 还有 {total_tests - passed_tests} 个问题需要进一步修复。")
    
    return summary

if __name__ == "__main__":
    main()