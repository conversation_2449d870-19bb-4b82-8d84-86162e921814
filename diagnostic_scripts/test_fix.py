#!/usr/bin/env python3
import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_websocket_dependencies():
    print("🔍 测试WebSocket启动条件修复...")
    
    try:
        from core.trading_system_initializer import TradingSystemInitializer
        
        # 创建初始化器
        initializer = TradingSystemInitializer()
        
        # 测试依赖检查
        test_symbols = ["BTC-USDT", "ETH-USDT"]
        result = await initializer._check_websocket_dependencies(test_symbols)
        
        print(f"✅ 依赖检查结果: {result['all_ready']}")
        print(f"📋 缺失依赖: {result.get('missing_dependencies', [])}")
        
        for check_name, check_result in result.get('checks', {}).items():
            status = check_result.get('status', 'unknown')
            icon = "✅" if status == "ready" else "⚠️" if status == "warning" else "❌"
            print(f"  {icon} {check_name}: {status}")
        
        return result['all_ready']
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_websocket_dependencies())
    print(f"\n🎯 修复效果: {'成功' if result else '失败'}")
