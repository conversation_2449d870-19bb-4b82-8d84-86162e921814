#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳错误精确诊断脚本
诊断DataSnapshotValidator中ensure_milliseconds_timestamp错误和Gate.io时间戳9000+ms异常
"""

import sys
import os
import time
import json
import traceback
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass

# 添加项目路径
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

@dataclass
class DiagnosisResult:
    """诊断结果"""
    test_name: str
    status: str  # "PASS", "FAIL", "ERROR"
    message: str
    details: Optional[Dict] = None
    error: Optional[str] = None

class TimestampErrorDiagnoser:
    """时间戳错误诊断器"""
    
    def __init__(self):
        self.results = []
        
    def run_all_diagnosis(self) -> Dict[str, Any]:
        """运行所有诊断测试"""
        print("🔥 开始时间戳错误精确诊断...")
        
        # 测试1: 模拟DataSnapshotValidator导入错误
        self.test_data_snapshot_validator_import()
        
        # 测试2: 测试统一时间戳处理器功能
        self.test_unified_timestamp_processor()
        
        # 测试3: 模拟Gate.io时间戳延迟问题
        self.test_gate_timestamp_delay()
        
        # 测试4: 测试跨交易所时间戳同步
        self.test_cross_exchange_sync()
        
        # 测试5: 测试函数作用域问题
        self.test_function_scope_issue()
        
        # 汇总结果
        return self.generate_summary()
    
    def test_data_snapshot_validator_import(self):
        """测试DataSnapshotValidator导入问题"""
        try:
            print("\n🔍 测试1: DataSnapshotValidator导入问题")
            
            # 模拟DataSnapshotValidator中的导入问题
            def problematic_function():
                """模拟有问题的函数"""
                try:
                    # 这种在函数内部导入可能导致作用域问题
                    from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp
                    
                    # 模拟使用场景
                    test_timestamp = 1754045988430
                    result = ensure_milliseconds_timestamp(test_timestamp)
                    
                    return True, result
                except Exception as e:
                    return False, str(e)
            
            success, result = problematic_function()
            
            if success:
                self.results.append(DiagnosisResult(
                    "DataSnapshotValidator导入测试",
                    "PASS",
                    f"函数内部导入成功，结果: {result}",
                    {"timestamp_result": result}
                ))
            else:
                self.results.append(DiagnosisResult(
                    "DataSnapshotValidator导入测试", 
                    "FAIL",
                    f"函数内部导入失败: {result}",
                    error=result
                ))
                
        except Exception as e:
            self.results.append(DiagnosisResult(
                "DataSnapshotValidator导入测试",
                "ERROR", 
                f"测试异常: {str(e)}",
                error=traceback.format_exc()
            ))
    
    def test_unified_timestamp_processor(self):
        """测试统一时间戳处理器功能"""
        try:
            print("\n🔍 测试2: 统一时间戳处理器功能")
            
            # 直接导入测试
            from websocket.unified_timestamp_processor import (
                ensure_milliseconds_timestamp, 
                calculate_data_age,
                get_synced_timestamp
            )
            
            # 测试ensure_milliseconds_timestamp
            test_cases = [
                1754045988.430,  # 秒级时间戳
                1754045988430,   # 毫秒级时间戳
                None,            # None值
                0,               # 零值
                -1               # 负值
            ]
            
            results = {}
            for i, timestamp in enumerate(test_cases):
                try:
                    result = ensure_milliseconds_timestamp(timestamp)
                    results[f"case_{i}"] = {"input": timestamp, "output": result, "status": "success"}
                except Exception as e:
                    results[f"case_{i}"] = {"input": timestamp, "error": str(e), "status": "error"}
            
            # 测试calculate_data_age
            current_time = time.time()
            data_timestamp = current_time - 1  # 1秒前的数据
            age = calculate_data_age(data_timestamp * 1000, current_time)  # 毫秒级输入
            
            # 测试get_synced_timestamp
            sync_timestamp = get_synced_timestamp("gate", None)
            
            self.results.append(DiagnosisResult(
                "统一时间戳处理器功能测试",
                "PASS",
                "所有核心函数正常工作",
                {
                    "ensure_milliseconds_tests": results,
                    "calculate_age_result": age,
                    "sync_timestamp": sync_timestamp
                }
            ))
            
        except Exception as e:
            self.results.append(DiagnosisResult(
                "统一时间戳处理器功能测试",
                "ERROR",
                f"测试异常: {str(e)}",
                error=traceback.format_exc()
            ))
    
    def test_gate_timestamp_delay(self):
        """测试Gate.io时间戳延迟问题"""
        try:
            print("\n🔍 测试3: Gate.io时间戳延迟问题")
            
            # 模拟Gate.io WebSocket数据
            current_time = int(time.time() * 1000)
            
            # 模拟不同的时间戳延迟场景
            scenarios = [
                {"name": "正常延迟", "delay_ms": 100},
                {"name": "轻微延迟", "delay_ms": 1000},
                {"name": "严重延迟", "delay_ms": 5000},
                {"name": "极端延迟", "delay_ms": 9000},  # 模拟9000ms延迟
            ]
            
            results = {}
            
            for scenario in scenarios:
                gate_timestamp = current_time - scenario["delay_ms"]
                okx_timestamp = current_time - 100  # OKX正常延迟100ms
                
                time_diff = abs(gate_timestamp - okx_timestamp)
                is_synced = time_diff <= 800  # 使用800ms阈值
                
                results[scenario["name"]] = {
                    "gate_timestamp": gate_timestamp,
                    "okx_timestamp": okx_timestamp,
                    "time_diff_ms": time_diff,
                    "is_synced": is_synced,
                    "threshold_ms": 800
                }
            
            # 检查是否发现了9000+ms问题
            extreme_delay = results["极端延迟"]
            if extreme_delay["time_diff_ms"] > 8000:
                status = "FAIL"
                message = f"发现极端时间戳延迟: {extreme_delay['time_diff_ms']}ms"
            else:
                status = "PASS"
                message = "时间戳延迟在正常范围内"
                
            self.results.append(DiagnosisResult(
                "Gate.io时间戳延迟测试",
                status,
                message,
                results
            ))
            
        except Exception as e:
            self.results.append(DiagnosisResult(
                "Gate.io时间戳延迟测试",
                "ERROR",
                f"测试异常: {str(e)}",
                error=traceback.format_exc()
            ))
    
    def test_cross_exchange_sync(self):
        """测试跨交易所时间戳同步"""
        try:
            print("\n🔍 测试4: 跨交易所时间戳同步测试")
            
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 获取各交易所的时间戳处理器
            exchanges = ["gate", "bybit", "okx"]
            processors = {}
            timestamps = {}
            
            for exchange in exchanges:
                processor = get_timestamp_processor(exchange)
                timestamp = processor.get_synced_timestamp(None)
                processors[exchange] = processor
                timestamps[exchange] = timestamp
            
            # 计算跨交易所时间差
            sync_results = {}
            for i, ex1 in enumerate(exchanges):
                for ex2 in exchanges[i+1:]:
                    time_diff = abs(timestamps[ex1] - timestamps[ex2])
                    is_synced = time_diff <= 800
                    
                    combo_name = f"{ex1}_vs_{ex2}"
                    sync_results[combo_name] = {
                        "exchange1": ex1,
                        "exchange2": ex2,
                        "timestamp1": timestamps[ex1],
                        "timestamp2": timestamps[ex2],
                        "time_diff_ms": time_diff,
                        "is_synced": is_synced
                    }
            
            # 检查是否有大于1000ms的时间差
            max_diff = max(result["time_diff_ms"] for result in sync_results.values())
            
            if max_diff > 1000:
                status = "FAIL"
                message = f"发现跨交易所时间戳不同步，最大差异: {max_diff}ms"
            else:
                status = "PASS"  
                message = f"跨交易所时间戳同步正常，最大差异: {max_diff}ms"
                
            self.results.append(DiagnosisResult(
                "跨交易所时间戳同步测试",
                status,
                message,
                {
                    "timestamps": timestamps,
                    "sync_results": sync_results,
                    "max_diff_ms": max_diff
                }
            ))
            
        except Exception as e:
            self.results.append(DiagnosisResult(
                "跨交易所时间戳同步测试",
                "ERROR",
                f"测试异常: {str(e)}",
                error=traceback.format_exc()
            ))
    
    def test_function_scope_issue(self):
        """测试函数作用域问题"""
        try:
            print("\n🔍 测试5: 函数作用域问题测试")
            
            # 测试不同的导入方式
            test_results = {}
            
            # 方式1: 模块顶部导入 (推荐)
            try:
                from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp as ems_top
                result1 = ems_top(1754045988430)
                test_results["top_level_import"] = {"status": "success", "result": result1}
            except Exception as e:
                test_results["top_level_import"] = {"status": "error", "error": str(e)}
            
            # 方式2: 函数内部导入 (可能有问题)
            def test_internal_import():
                try:
                    from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp
                    return ensure_milliseconds_timestamp(1754045988430)
                except Exception as e:
                    raise e
            
            try:
                result2 = test_internal_import()
                test_results["internal_import"] = {"status": "success", "result": result2}
            except Exception as e:
                test_results["internal_import"] = {"status": "error", "error": str(e)}
            
            # 方式3: 条件导入 (更可能有问题)
            def test_conditional_import(condition=True):
                if condition:
                    from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp
                    return ensure_milliseconds_timestamp(1754045988430)
                else:
                    return None
            
            try:
                result3 = test_conditional_import()
                test_results["conditional_import"] = {"status": "success", "result": result3}
            except Exception as e:
                test_results["conditional_import"] = {"status": "error", "error": str(e)}
            
            # 分析结果
            error_count = sum(1 for r in test_results.values() if r["status"] == "error")
            
            if error_count > 0:
                status = "FAIL"
                message = f"发现{error_count}个导入作用域问题"
            else:
                status = "PASS"
                message = "所有导入方式都正常工作"
                
            self.results.append(DiagnosisResult(
                "函数作用域问题测试",
                status,
                message,
                test_results
            ))
            
        except Exception as e:
            self.results.append(DiagnosisResult(
                "函数作用域问题测试",
                "ERROR",
                f"测试异常: {str(e)}",
                error=traceback.format_exc()
            ))
    
    def generate_summary(self) -> Dict[str, Any]:
        """生成诊断摘要"""
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.status == "PASS"])
        failed_tests = len([r for r in self.results if r.status == "FAIL"])
        error_tests = len([r for r in self.results if r.status == "ERROR"])
        
        print(f"\n📊 诊断摘要:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   错误: {error_tests}")
        
        # 分析关键问题
        critical_issues = []
        for result in self.results:
            if result.status in ["FAIL", "ERROR"]:
                critical_issues.append({
                    "test": result.test_name,
                    "status": result.status,
                    "message": result.message
                })
        
        # 生成修复建议
        recommendations = []
        
        # 检查是否有导入作用域问题
        scope_test = next((r for r in self.results if "作用域" in r.test_name), None)
        if scope_test and scope_test.status == "FAIL":
            recommendations.append("将ensure_milliseconds_timestamp导入移到模块顶部")
        
        # 检查是否有时间戳延迟问题
        delay_test = next((r for r in self.results if "延迟" in r.test_name), None)
        if delay_test and delay_test.status == "FAIL":
            recommendations.append("修复Gate.io时间戳处理逻辑，减少延迟到1000ms内")
        
        # 检查是否有同步问题
        sync_test = next((r for r in self.results if "同步" in r.test_name), None)
        if sync_test and sync_test.status == "FAIL":
            recommendations.append("统一所有交易所的时间戳处理机制")
        
        summary = {
            "timestamp": int(time.time()),
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_tests": total_tests,
            "passed_tests": passed_tests, 
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            "critical_issues": critical_issues,
            "recommendations": recommendations,
            "detailed_results": [
                {
                    "test_name": r.test_name,
                    "status": r.status,
                    "message": r.message,
                    "details": r.details,
                    "error": r.error
                } for r in self.results
            ]
        }
        
        return summary

def main():
    """主函数"""
    diagnoser = TimestampErrorDiagnoser()
    summary = diagnoser.run_all_diagnosis()
    
    # 保存诊断结果
    output_file = f"timestamp_diagnosis_{int(time.time())}.json"
    output_path = os.path.join(os.path.dirname(__file__), '..', 'diagnostic_results', output_file)
    
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 诊断结果已保存到: {output_path}")
    
    # 显示关键发现
    print(f"\n🔥 关键发现:")
    for issue in summary["critical_issues"]:
        print(f"   ❌ {issue['test']}: {issue['message']}")
    
    print(f"\n💡 修复建议:")
    for i, rec in enumerate(summary["recommendations"], 1):
        print(f"   {i}. {rec}")
    
    return summary

if __name__ == "__main__":
    main()