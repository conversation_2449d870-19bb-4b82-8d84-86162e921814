#!/usr/bin/env python3
import asyncio
import time
import socket

async def test_network():
    print("🔍 网络连接诊断...")
    
    # 1. DNS解析测试
    print("🌐 DNS解析测试:")
    domains = ["api.gateio.ws", "api.bybit.com", "www.okx.com", "google.com"]
    for domain in domains:
        try:
            ip = socket.gethostbyname(domain)
            print(f"  ✅ {domain}: {ip}")
        except Exception as e:
            print(f"  ❌ {domain}: {e}")
    
    # 2. 基础连接测试
    print("\n📡 基础连接测试:")
    try:
        import urllib.request
        response = urllib.request.urlopen("https://www.google.com", timeout=10)
        print(f"  ✅ HTTP连接: {response.getcode()}")
    except Exception as e:
        print(f"  ❌ HTTP连接: {e}")
    
    # 3. 系统依赖检查
    print("\n📦 系统依赖检查:")
    deps = ["asyncio", "socket", "time", "json"]
    for dep in deps:
        try:
            __import__(dep)
            print(f"  ✅ {dep}: 可用")
        except ImportError:
            print(f"  ❌ {dep}: 缺失")
    
    # 4. 检查aiohttp
    try:
        import aiohttp
        print(f"  ✅ aiohttp: 可用")
    except ImportError:
        print(f"  ❌ aiohttp: 缺失")
    
    print("\n🎯 诊断完成")

if __name__ == "__main__":
    asyncio.run(test_network())
